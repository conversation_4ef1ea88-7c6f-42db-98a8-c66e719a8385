{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@mantine/core": "^5.10.5", "@mantine/hooks": "^5.10.5", "@mantine/rte": "^5.10.5", "@tiptap/extension-character-count": "^2.25.0", "@tiptap/extension-color": "^2.25.0", "@tiptap/extension-focus": "^2.25.0", "@tiptap/extension-font-family": "^2.25.0", "@tiptap/extension-gapcursor": "^2.25.0", "@tiptap/extension-highlight": "^2.25.0", "@tiptap/extension-horizontal-rule": "^2.25.0", "@tiptap/extension-image": "^2.25.0", "@tiptap/extension-link": "^2.25.0", "@tiptap/extension-placeholder": "^2.25.0", "@tiptap/extension-strike": "^2.25.0", "@tiptap/extension-subscript": "^2.25.0", "@tiptap/extension-superscript": "^2.25.0", "@tiptap/extension-table": "^2.25.0", "@tiptap/extension-table-cell": "^2.25.0", "@tiptap/extension-table-header": "^2.25.0", "@tiptap/extension-table-row": "^2.25.0", "@tiptap/extension-text-align": "^2.25.0", "@tiptap/extension-text-style": "^2.25.0", "@tiptap/extension-typography": "^2.25.0", "@tiptap/extension-underline": "^2.25.0", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "axios": "^1.8.4", "bootstrap": "^5.3.7", "cra-template": "1.2.0", "date-fns": "^2.30.0", "firebase": "^10.0.0", "lucide-react": "^0.518.0", "react": "^19.0.0", "react-bootstrap": "^2.10.10", "react-dom": "^19.0.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^7.5.1", "react-scripts": "5.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/js": "9.23.0", "autoprefixer": "^10.4.20", "eslint": "9.23.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-react": "7.37.4", "globals": "15.15.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17"}}