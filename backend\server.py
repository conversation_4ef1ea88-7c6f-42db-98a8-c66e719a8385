from fastapi import <PERSON><PERSON><PERSON>, APIRouter, HTTPException, Depends, UploadFile, File, Form, status, Request
from fastapi.staticfiles import StaticFiles
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.encoders import jsonable_encoder
from bson import ObjectId # Added for ObjectId type checking
from datetime import datetime # Added for datetime type checking
from dotenv import load_dotenv
from starlette.middleware.cors import CORSMiddleware
from motor.motor_asyncio import AsyncIOMotorClient
import os
import logging
from pathlib import Path
from pydantic import BaseModel, Field, EmailStr
from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime, timedelta
import firebase_admin
from firebase_admin import auth, credentials
import json
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig, MessageType
import base64
from io import BytesIO
from PIL import Image
from bson import ObjectId
import bcrypt
import jwt
import random
import string

# PDF Generation imports
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image as RLImage, Flowable
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, mm
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from reportlab.lib.colors import HexColor
from pathlib import Path
from reportlab.pdfgen import canvas
from reportlab.lib.colors import HexColor
from fastapi.responses import StreamingResponse

ROOT_DIR = Path(__file__).parent
load_dotenv(ROOT_DIR / '.env')

# MongoDB connection
mongo_url = os.environ['MONGO_URL']
client = AsyncIOMotorClient(mongo_url)
db = client[os.environ['DB_NAME']]

# Firebase Admin SDK setup with proper credentials
firebase_config = ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Initialize Firebase (for production, use real credentials)
firebase_initialized = False
try:
    cred = credentials.Certificate(firebase_config)
    firebase_admin.initialize_app(cred)
    firebase_initialized = True
except Exception as e:
    print(f"Firebase initialization failed: {e}")
    firebase_initialized = False

# JWT Secret for local authentication
JWT_SECRET = "vibrant_yoga_secret_key_2025"
JWT_ALGORITHM = "HS256"

# Custom JSON Encoder for ObjectId
class JSONEncoder(json.JSONEncoder):
    def default(self, obj: Any) -> Any:
        if isinstance(obj, ObjectId):
            return str(obj)
        if isinstance(obj, datetime): # Add datetime handling
            return obj.isoformat()
        return super().default(obj)

def serialize_doc(doc: dict) -> dict:
    """Convert MongoDB document to JSON serializable format"""
    if doc is None:
        return None
    
    # Handle the primary ID:
    # If your application 'id' (UUID) is already present, keep it.
    # Only use MongoDB's '_id' to populate 'id' if 'id' is not already present.
    if '_id' in doc:
        if 'id' not in doc or doc['id'] is None: # Check if app 'id' exists and is not None
            doc['id'] = str(doc['_id'])
        del doc['_id'] # Always remove the MongoDB '_id' field after processing
    
    # Convert any other ObjectId fields and datetime objects
    for key, value in doc.items():
        if isinstance(value, ObjectId):
            doc[key] = str(value)
        elif isinstance(value, datetime): # Add datetime serialization
            doc[key] = value.isoformat()
        elif isinstance(value, list):
            # Recursively serialize items in a list if they are dicts (sub-documents)
            # or convert if they are ObjectId/datetime
            new_list = []
            for item in value:
                if isinstance(item, dict):
                    new_list.append(serialize_doc(item)) # Recursive call for sub-documents
                elif isinstance(item, ObjectId):
                    new_list.append(str(item))
                elif isinstance(item, datetime):
                    new_list.append(item.isoformat())
                else:
                    new_list.append(item)
            doc[key] = new_list
        elif isinstance(value, dict) and key != "preferences" and key != "booking_summary" and key != "pricing": 
            # Avoid double-serializing known dict fields that don't contain ObjectIds/datetimes
            # or handle them specifically if they might. For now, assume other dicts might need serialization.
            doc[key] = serialize_doc(value) # Recursive call for nested dictionaries

    return doc

# Create the main app with lifespan management
from contextlib import asynccontextmanager

# Lifespan context manager
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup code
    yield
    # Shutdown code
    client.close()
    logger.info("MongoDB client closed")

# Security instances
security = HTTPBearer()
security_optional = HTTPBearer(auto_error=False)

# Create API router first
api_router = APIRouter()

app = FastAPI(
    title="Vibrant Yoga API",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
origins = ["*"]


app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix="/api")

# Set custom JSON encoder
app.json_encoder = JSONEncoder

# Security
security = HTTPBearer()
security_optional = HTTPBearer(auto_error=False)

def generate_booking_id():
    """Generate a 5-character uppercase alphanumeric booking ID."""
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=5))

# Models
class User(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    firebase_id: Optional[str] = None
    name: str
    email: EmailStr
    password_hash: Optional[str] = None
    role: str = "user"  # user or admin
    status: str = "active"  # active or suspended
    created_at: datetime = Field(default_factory=datetime.utcnow)
    preferences: Dict[str, Any] = Field(default_factory=dict)
    booking_summary: Dict[str, Any] = Field(default_factory=dict)

class Testimonial(BaseModel):
    id: Optional[str] = Field(None, alias='_id')
    name: str
    role: str
    content: str
    rating: int = Field(..., ge=1, le=5)
    avatar_path: Optional[str] = None

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

class Event(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    title: str
    description: str
    event_type: str  # 'single' or 'group'
    price: float
    date: str  # YYYY-MM-DD format, start date for group events
    end_date: Optional[str] = None # YYYY-MM-DD format, end date for group events
    time: str  # HH:MM format
    timezone: Optional[str] = None
    qr_code_base64: Optional[str] = None
    upi_id: Optional[str] = None
    is_online: bool = True
    session_link: Optional[str] = None
    capacity: int = 50
    waitlist_enabled: bool = True
    delivery_mode: str = "online"  # online, offline, hybrid
    created_at: datetime = Field(default_factory=datetime.utcnow)
    created_by: str  # admin user id
    available_slots: Optional[int] = None

class Booking(BaseModel):
    id: str = Field(default_factory=generate_booking_id)
    user_id: str
    event_id: str
    amount: float
    payment_proof_base64: Optional[str] = None
    utr_number: Optional[str] = None
    status: str = "pending"  # pending, approved, rejected
    admin_notes: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    approved_at: Optional[datetime] = None

class SMTPSettings(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    mailer_name: str
    host: str
    port: int
    username: str
    email: str
    encryption: str
    password: str

class EmailRequest(BaseModel):
    to_email: EmailStr
    subject: str
    body: str

class AdImage(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    original_filename: str
    stored_filename: str
    file_path: str
    file_size: int  # in bytes
    mime_type: str
    upload_timestamp: datetime = Field(default_factory=datetime.utcnow)
    uploaded_by: str  # user ID who uploaded the image

    class Config:
        allow_population_by_field_name = True
        arbitrary_types_allowed = True
        json_encoders = {ObjectId: str}

# Request/Response Models

class AdminExistsResponse(BaseModel):
    admin_exists: bool


class GoogleToken(BaseModel):
    token: str

class UserCreate(BaseModel):
    name: str
    email: EmailStr
    password: str

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class EventCreate(BaseModel):
    title: str
    description: str
    event_type: str  # 'single' or 'group'
    price: float
    date: str
    end_date: Optional[str] = None
    time: str
    timezone: Optional[str] = None
    upi_id: Optional[str] = None
    is_online: bool = True
    session_link: Optional[str] = None
    capacity: int = 50
    delivery_mode: str = "online"

class EventUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    event_type: Optional[str] = None
    price: Optional[float] = None
    date: Optional[str] = None
    end_date: Optional[str] = None
    time: Optional[str] = None
    timezone: Optional[str] = None
    upi_id: Optional[str] = None
    is_online: Optional[bool] = None
    session_link: Optional[str] = None
    capacity: Optional[int] = None
    delivery_mode: Optional[str] = None

class BookingCreate(BaseModel):
    event_id: str

class BookingUpdate(BaseModel):
    status: str
    admin_notes: Optional[str] = None

class TokenResponse(BaseModel):
    access_token: str
    token_type: str
    user: User

# Utility Functions
def hash_password(password: str) -> str:
    """Hash password using bcrypt"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(password: str, hashed_password: str) -> bool:
    """Verify password against hash"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))

def create_jwt_token(user_data: dict) -> str:
    """Create JWT token for user"""
    payload = {
        "user_id": user_data["id"],
        "email": user_data["email"],
        "role": user_data["role"],
        "exp": datetime.utcnow() + timedelta(days=7)
    }
    print(f"Creating token with payload: {payload}")
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

def verify_jwt_token(token: str) -> dict:
    """Verify and decode JWT token"""
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.InvalidTokenError:
        raise HTTPException(status_code=401, detail="Invalid token")
    except Exception as e:
        raise HTTPException(status_code=401, detail=f"Token verification error: {str(e)}")

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Get current user from JWT token"""
    token = credentials.credentials
    print(f"get_current_user: Attempting to verify token (first 20 chars): {token[:20]}...")
    try:
        payload = verify_jwt_token(token)
        user_id_from_payload = payload.get("user_id")
        print(f"get_current_user: Token verified. Payload user_id: {user_id_from_payload}")
        
        if not user_id_from_payload:
            print("get_current_user: CRITICAL - user_id missing in token payload!")
            raise HTTPException(status_code=401, detail="User ID missing in token")

        print(f"get_current_user: Querying DB for user_id: '{user_id_from_payload}'")
        user_doc = await db.users.find_one({"id": user_id_from_payload})
        
        if not user_doc:
            print(f"get_current_user: CRITICAL - User not found in DB for user_id: '{user_id_from_payload}'. Attempting fallback by email.")
            email_from_payload = payload.get("email")
            if email_from_payload:
                print(f"get_current_user: Fallback - Querying DB for email: '{email_from_payload}'")
                fallback_user_doc = await db.users.find_one({"email": email_from_payload})
                if fallback_user_doc:
                    print(f"get_current_user: Fallback - User FOUND by email. Their DB id is '{fallback_user_doc.get('id')}', MongoDB _id is '{fallback_user_doc.get('_id')}'. Token expected id '{user_id_from_payload}'.")
                else:
                    print(f"get_current_user: Fallback - User NOT found by email '{email_from_payload}' either.")
            else:
                print("get_current_user: Fallback - No email in token payload to attempt fallback.")
            raise HTTPException(status_code=401, detail="User not found via token (id lookup failed)")
        
        print(f"get_current_user: User found in DB: email='{user_doc.get('email')}', id='{user_doc.get('id')}'")
        return serialize_doc(user_doc)
    except HTTPException as e:
        print(f"get_current_user: HTTPException occurred: {e.detail}")
        raise e
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        print(f"get_current_user: Unexpected authentication error: {str(e)}\nTrace: {error_trace}")
        raise HTTPException(status_code=401, detail=f"Unexpected authentication error: {str(e)}")

async def get_admin_user(current_user: dict = Depends(get_current_user)):
    """Ensure current user is admin"""
    if current_user.get("role") != "admin":
        raise HTTPException(status_code=403, detail="Admin access required")
    return current_user

async def get_current_user_or_none(credentials: HTTPAuthorizationCredentials = Depends(security_optional)):
    """Get current user but don't raise exception if not authenticated"""
    if not credentials:
        return None
    try:
        token = credentials.credentials
        payload = verify_jwt_token(token)
        user_id = payload.get("user_id")
        if not user_id:
            return None
            
        user_doc = await db.users.find_one({"id": user_id})
        if not user_doc:
            return None
            
        return serialize_doc(user_doc)
    except Exception:
        return None

async def send_email(to_email: str, subject: str, body: str, attachments: List[Dict[str, Any]] = None):
    """Send email using SMTP settings with optional file attachments
    
    Args:
        to_email: Recipient email address
        subject: Email subject
        body: Email body (HTML)
        attachments: List of attachment dictionaries with 'filename' and 'content' keys
    """
    try:
        # Get SMTP settings
        smtp_settings_raw = await db.smtp_settings.find_one({})
        if not smtp_settings_raw:
            print("Using default SMTP settings")
            smtp_settings = SMTPSettings().dict()
        else:
            print("Using custom SMTP settings from database")
            smtp_settings = serialize_doc(smtp_settings_raw)
        
        print(f"SMTP Settings: host={smtp_settings['host']}, port={smtp_settings['port']}, username={smtp_settings['username']}")
        
        # Configure FastAPI-Mail
        conf = ConnectionConfig(
            MAIL_USERNAME=smtp_settings['username'],
            MAIL_PASSWORD=smtp_settings['password'],
            MAIL_FROM=smtp_settings['email'],
            MAIL_PORT=smtp_settings['port'],
            MAIL_SERVER=smtp_settings['host'],
            MAIL_SSL_TLS=True,
            MAIL_STARTTLS=False,
            USE_CREDENTIALS=True,
            VALIDATE_CERTS=True
        )
        
        # Create FastMail instance
        fm = FastMail(conf)
        
        # Create message schema
        # Prepare message with optional attachments
        message = MessageSchema(
            subject=subject,
            recipients=[to_email],
            body=body,
            subtype=MessageType.html,
            attachments=attachments or []
        )
        
        print(f"Sending email to {to_email} with subject: {subject}")
        if attachments:
            if isinstance(attachments[0], dict) and 'filename' in attachments[0]:
                print(f"Attachments: {[a['filename'] for a in attachments]}")
            else:
                print(f"Attachments: {attachments}")
        
        print("Sending email...")
        await fm.send_message(message)
        print("Email sent successfully!")
        return True
            
    except Exception as e:
        print(f"Email sending failed with error: {str(e)}")
        import traceback
        print("Full error details:")
        print(traceback.format_exc())
        return False

@api_router.post("/email/send", response_model=dict, tags=["Email"])
async def create_email(request: EmailRequest):
    """Send an email using the configured SMTP settings"""
    success = await send_email(
        to_email=request.to_email,
        subject=request.subject,
        body=request.body
    )
    
    if not success:
        raise HTTPException(status_code=500, detail="Failed to send email")
    
    return {"message": "Email sent successfully"}

def convert_image_to_base64(image_data: bytes) -> str:
    """Convert image bytes to base64 string"""
    try:
        image = Image.open(BytesIO(image_data))
        buffered = BytesIO()
        image.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode()
        return f"data:image/png;base64,{img_str}"
    except Exception as e:
        print(f"Image conversion failed: {e}")
        return ""

# Admin Existence Check Route
@api_router.get("/admin_exists", response_model=AdminExistsResponse)
async def check_admin_exists():
    """Check if an admin user exists in the database."""
    admin_user = await db.users.find_one({"role": "admin"})
    return AdminExistsResponse(admin_exists=bool(admin_user))

# Authentication Routes
@api_router.post("/auth/register", response_model=TokenResponse)
async def register(request: UserCreate):
    """Register new user"""
    # Check if user already exists
    existing_user = await db.users.find_one({"email": request.email})
    if existing_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Hash password
    password_hash = hash_password(request.password)
    
    # Create user
    user_data = User(
        name=request.name,
        email=request.email,
        password_hash=password_hash
    )
    
    await db.users.insert_one(user_data.dict())
    
    # Create token
    token = create_jwt_token(user_data.dict())
    
    return TokenResponse(
        access_token=token,
        token_type="bearer",
        user=user_data
    )

@api_router.post("/auth/login", response_model=TokenResponse)
async def login(request: UserLogin):
    """Login user with email/password"""
    # Find user
    user_doc = await db.users.find_one({"email": request.email})
    if not user_doc:
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    print(f"login: Raw user_doc by email: {user_doc}") 
    user_data = serialize_doc(user_doc)
    
    # Verify password
    if "password_hash" not in user_data:
        raise HTTPException(status_code=401, detail="Invalid credentials - no password hash found")
    
    if not verify_password(request.password, user_data["password_hash"]):
        raise HTTPException(status_code=401, detail="Invalid credentials")
    
    # Create token
    print(f"login: User data for token: id='{user_data.get('id')}', email='{user_data.get('email')}'")
    token = create_jwt_token(user_data)
    
    # Remove password hash from response
    user_data.pop("password_hash", None)
    
    return TokenResponse(
        access_token=token,
        token_type="bearer",
        user=User(**user_data)
    )

@api_router.post("/auth/google", response_model=TokenResponse)
async def google_login(request: GoogleToken):
    """Login or register user with Google Firebase ID token"""
    try:
        decoded_token = firebase_admin.auth.verify_id_token(request.token)
    except firebase_admin.auth.FirebaseAuthError as e:
        raise HTTPException(status_code=401, detail=f"Invalid Firebase token: {e}")

    firebase_uid = decoded_token.get("uid")
    email = decoded_token.get("email")
    name = decoded_token.get("name", email.split('@')[0] if email else "Google User")

    if not firebase_uid or not email:
        raise HTTPException(status_code=400, detail="Invalid Firebase token: UID or email missing")

    user_doc = await db.users.find_one({"firebase_id": firebase_uid})
    if not user_doc:
        # If no user with firebase_uid, check by email to link accounts
        user_doc = await db.users.find_one({"email": email})
        if user_doc:
            # User exists with this email, link firebase_id
            await db.users.update_one(
                {"email": email},
                {"$set": {"firebase_id": firebase_uid}}
            )
            user_doc["firebase_id"] = firebase_uid # Update in-memory doc
        else:
            # New user, create them
            new_user_data = User(
                firebase_id=firebase_uid,
                name=name,
                email=email,
                role="user",
                status="active"
            )
            await db.users.insert_one(new_user_data.dict())
            user_doc = new_user_data.dict()
    
    # user_doc is now a dictionary, either from DB or new_user_data.dict()
    user_data_for_response_and_token = serialize_doc(user_doc.copy())
    
    # Remove password_hash before creating token and for response model instantiation
    # This handles cases where an existing email/password user links their Google account
    user_data_for_response_and_token.pop("password_hash", None)

    # create_jwt_token expects 'id' in user_data_for_response_and_token
    # serialize_doc ensures _id from DB is converted to 'id',
    # or 'id' from Pydantic model (new_user_data.dict()) is preserved.
    token = create_jwt_token(user_data_for_response_and_token)
    
    # Create User model for the response from data that no longer has password_hash
    response_user = User(**user_data_for_response_and_token)

    return TokenResponse(
        access_token=token,
        token_type="bearer",
        user=response_user
    )

# User Routes
@api_router.get("/users/me", response_model=User)
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """Get current user information"""
    current_user.pop("password_hash", None)
    return User(**current_user)

@api_router.get("/users", response_model=List[User])
async def get_users(current_user: dict = Depends(get_admin_user)):
    """Get all users (admin only)"""
    users_cursor = db.users.find({})
    users_raw = await users_cursor.to_list(1000)
    users = []
    for user_doc in users_raw:
        user_data = serialize_doc(user_doc)
        user_data.pop("password_hash", None)
        users.append(User(**user_data))
    return users

@api_router.put("/users/{user_id}/role")
async def update_user_role(
    user_id: str, 
    role: str,
    current_user: dict = Depends(get_admin_user)
):
    """Update user role (admin only)"""
    if role not in ["user", "admin"]:
        raise HTTPException(status_code=400, detail="Invalid role")
    
    result = await db.users.update_one(
        {"id": user_id},
        {"$set": {"role": role}}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="User not found")
    
    return {"message": "User role updated successfully"}

# Event Routes
@api_router.get("/events", response_model=List[Event])
async def get_events():
    """Get all events"""
    events_cursor = db.events.find()
    events = []
    async for event_doc in events_cursor:
        event_data = serialize_doc(event_doc)
        # Handle backward compatibility for old event structure
        if 'pricing' in event_data and isinstance(event_data['pricing'], dict):
            event_data['price'] = event_data['pricing'].get('daily', 0)
            event_data['event_type'] = 'single'
            del event_data['pricing']

        # Ensure required fields have default values if missing
        if 'event_type' not in event_data:
            event_data['event_type'] = 'single'
        if 'price' not in event_data:
            event_data['price'] = 0
        if 'end_date' not in event_data:
            event_data['end_date'] = None

        bookings_count = await db.bookings.count_documents({"event_id": event_data["id"]})
        capacity = event_data.get("capacity", 0)
        event_data["available_slots"] = capacity - bookings_count
            
        try:
            events.append(Event(**event_data))
        except Exception as e:
            print(f"Could not process event: {event_data.get('id')}. Error: {e}")

    return events

@api_router.post("/events", response_model=Event)
async def create_event(
    event: EventCreate,
    current_user: dict = Depends(get_admin_user)
):
    """Create new event (admin only)"""
    event_data = Event(
        title=event.title,
        description=event.description,
        event_type=event.event_type,
        price=event.price,
        date=event.date,
        end_date=event.end_date,
        time=event.time,
        timezone=event.timezone,
        upi_id=event.upi_id,
        is_online=event.is_online,
        session_link=event.session_link,
        capacity=event.capacity,
        delivery_mode=event.delivery_mode,
        created_by=current_user["id"]
    )
    
    await db.events.insert_one(event_data.dict())
    return event_data

@api_router.get("/events/{event_id}", response_model=Event)
async def get_event(event_id: str):
    """Get event by ID"""
    event_doc = await db.events.find_one({"id": event_id})
    if not event_doc:
        raise HTTPException(status_code=404, detail="Event not found")
    
    event_data = serialize_doc(event_doc)
    return Event(**event_data)

@api_router.delete("/events/{event_id}", status_code=200)
async def delete_event(
    event_id: str,
    current_user: dict = Depends(get_admin_user)
):
    """Deletes an event from the database."""
    result = await db.events.delete_one({"id": event_id})

    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Event not found")

    return {"message": "Event deleted successfully"}

@api_router.put("/events/{event_id}", response_model=Event)
async def update_event(
    event_id: str,
    event_update: EventUpdate,
    current_user: dict = Depends(get_admin_user)
):
    """Update an existing event (admin only)"""
    update_data = event_update.dict(exclude_unset=True)

    if not update_data:
        raise HTTPException(status_code=400, detail="No update data provided")

    result = await db.events.update_one(
        {"id": event_id},
        {"$set": update_data}
    )

    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Event not found")

    updated_event_doc = await db.events.find_one({"id": event_id})
    return Event(**serialize_doc(updated_event_doc))

@api_router.post("/events/{event_id}/qr-code")
async def upload_qr_code(
    event_id: str,
    file: UploadFile = File(...),
    current_user: dict = Depends(get_admin_user)
):
    """Upload QR code for event (admin only)"""
    try:
        # Read file content
        file_content = await file.read()
        
        # Convert to base64
        qr_code_base64 = convert_image_to_base64(file_content)
        
        # Update event
        result = await db.events.update_one(
            {"id": event_id},
            {"$set": {"qr_code_base64": qr_code_base64}}
        )
        
        if result.matched_count == 0:
            raise HTTPException(status_code=404, detail="Event not found")
        
        return {"message": "QR code uploaded successfully"}
    
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"File upload failed: {str(e)}")

# Booking Routes
@api_router.post("/bookings", response_model=Booking)
async def create_booking(
    booking: BookingCreate,
    current_user: dict = Depends(get_current_user)
):
    """Create new booking"""
    # Check if event exists
    event_doc = await db.events.find_one({"id": booking.event_id})
    if not event_doc:
        raise HTTPException(status_code=404, detail="Event not found")
    
    event_data = serialize_doc(event_doc)
    
    # Get price from the event
    amount = event_data.get("price")
    if amount is None:
        raise HTTPException(status_code=400, detail="Event price is not set")
    
    booking_data = Booking(
        user_id=current_user["id"],
        event_id=booking.event_id,
        amount=amount
    )
    
    booking_to_insert = booking_data.dict()
    await db.bookings.insert_one(booking_to_insert)
    
    # Send confirmation email
    await send_email(
        to_email=current_user["email"],
        subject="Booking Confirmation - Vibrant Yoga",
        body=f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Vibrant Yoga - Booking Confirmation</title>
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
                body {{
                    font-family: 'Poppins', Arial, sans-serif;
                    line-height: 1.6;
                    color: #4B5563;
                    margin: 0;
                    padding: 0;
                    background-color: #F9FAFB;
                }}
                .email-container {{
                    max-width: 600px;
                    margin: 0 auto;
                    background-color: #FFFFFF;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
                }}
                .email-header {{
                    background-color: #553C9A;
                    padding: 20px;
                    text-align: center;
                }}
                .email-header h1 {{
                    color: white;
                    margin: 0;
                    font-size: 24px;
                    font-weight: 600;
                }}
                .email-body {{
                    padding: 30px;
                }}
                .greeting {{
                    font-size: 18px;
                    font-weight: 500;
                    margin-bottom: 20px;
                }}
                .booking-status {{
                    font-size: 16px;
                    margin-bottom: 25px;
                }}
                .booking-status .status {{
                    display: inline-block;
                    padding: 6px 15px;
                    border-radius: 20px;
                    font-weight: 600;
                    color: white;
                    background-color: #F59E0B;
                    margin: 0 5px;
                }}
                .section-title {{
                    font-size: 16px;
                    font-weight: 600;
                    color: #553C9A;
                    margin-top: 25px;
                    margin-bottom: 10px;
                    border-bottom: 2px solid #F3F4F6;
                    padding-bottom: 8px;
                }}
                .details-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 25px;
                }}
                .details-table td {{
                    padding: 10px;
                    border: 1px solid #E5E7EB;
                }}
                .details-table .label {{
                    font-weight: 500;
                    background-color: #F9F7FF;
                    width: 35%;
                }}
                .details-table .value {{
                    width: 65%;
                }}
                .notice-box {{
                    background-color: #EEF2FF; /* Light blue background */
                    border-left: 4px solid #6366F1; /* Indigo border */
                    padding: 15px;
                    margin-top: 20px;
                    margin-bottom: 20px;
                    border-radius: 4px;
                }}
                .notice-box strong {{
                    color: #4F46E5; /* Indigo text for emphasis */
                }}
                .footer {{
                    background-color: #F9F7FF;
                    padding: 20px;
                    text-align: center;
                    font-size: 14px;
                }}
                .thank-you {{
                    font-size: 16px;
                    font-weight: 500;
                    color: #553C9A;
                    margin-top: 30px;
                    text-align: center;
                }}
                .contact-info {{
                    margin-top: 5px;
                    color: #6B7280;
                    font-size: 13px;
                    text-align: center;
                }}
                .cta-button {{
                    display: inline-block;
                    background-color: #553C9A;
                    color: white !important;
                    text-decoration: none;
                    padding: 12px 25px;
                    border-radius: 5px;
                    font-weight: 500;
                    margin-top: 20px;
                }}
                .button-container {{
                    text-align: center;
                    margin: 25px 0;
                }}
                .highlight {{
                    color: #553C9A;
                    font-weight: 600;
                }}
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="email-header">
                    <h1>Vibrant Yoga</h1>
                </div>
                <div class="email-body">
                    <div class="greeting">Dear {current_user['name']},</div>
                    <div class="booking-status">
                        Your booking for <strong>"{event_data['title']}"</strong> has been <span class="status">submitted</span> and is pending approval.
                    </div>
                    
                    <div class="section-title">Event Details</div>
                    <table class="details-table">
                        <tr>
                            <td class="label">Event Name</td>
                            <td class="value">{event_data['title']}</td>
                        </tr>
                        <tr>
                            <td class="label">Date</td>
                            <td class="value">{event_data['date']}</td>
                        </tr>
                        <tr>
                            <td class="label">Time</td>
                            <td class="value">{event_data['time']} {event_data.get('timezone', '')}</td>
                        </tr>
                        <tr>
                            <td class="label">Event Type</td>
                            <td class="value">{event_data.get('event_type', 'N/A').title()}</td>
                        </tr>
                        <tr>
                            <td class="label">Amount</td>
                            <td class="value">₹{amount}</td>
                        </tr>
                    </table>
                    
                    <div class="notice-box">
                        <strong>Next Steps:</strong><br>
                        Please upload your payment proof to complete the booking. Once your payment is verified, you will receive a confirmation email.
                    </div>
                    
                    <div class="button-container">
                        <a href="https://vibrantyoga.com/my-bookings" class="cta-button">Manage Your Booking</a>
                    </div>
                    
                    <div class="thank-you">Thank you for choosing Vibrant Yoga!</div>
                    <div class="contact-info">If you have any questions, please contact <NAME_EMAIL></div>
                </div>
                <div class="footer">
                    &copy; {datetime.now().year} Vibrant Yoga. All rights reserved.
                </div>
            </div>
        </body>
        </html>
        """
    )
    
    return booking_to_insert

@api_router.get("/bookings")
async def get_bookings(current_user: dict = Depends(get_current_user)):
    """Get user's bookings with user details for admin view"""
    if current_user["role"] == "admin":
        bookings_cursor = db.bookings.find({})
    else:
        bookings_cursor = db.bookings.find({"user_id": current_user["id"]})
    
    bookings_raw = await bookings_cursor.to_list(1000)
    bookings = []
    
    # Create a cache of user information to avoid multiple lookups
    user_cache = {}
    
    for booking_doc in bookings_raw:
        booking_data = serialize_doc(booking_doc)
        
        # For admin view, include user information
        if current_user["role"] == "admin":
            user_id = booking_data["user_id"]
            
            # Get user info from cache or from database if not in cache
            if user_id not in user_cache:
                user_doc = await db.users.find_one({"id": user_id})
                if user_doc:
                    user_cache[user_id] = {
                        "name": user_doc.get("name", "Unknown"),
                        "email": user_doc.get("email", "")
                    }
                else:
                    user_cache[user_id] = {"name": "Unknown User", "email": ""}
            
            # Add user details to booking data
            booking_data["user_name"] = user_cache[user_id]["name"]
            booking_data["user_email"] = user_cache[user_id]["email"]
        
        bookings.append(booking_data)
    
    return bookings

@api_router.put("/bookings/{booking_id}/status")
async def update_booking_status(
    booking_id: str,
    update: BookingUpdate,
    current_user: dict = Depends(get_admin_user)
):
    """Update booking status (admin only)"""
    if update.status not in ["pending", "approved", "rejected"]:
        raise HTTPException(status_code=400, detail="Invalid status")
    
    # Get booking details
    booking_doc = await db.bookings.find_one({"id": booking_id})
    if not booking_doc:
        raise HTTPException(status_code=404, detail="Booking not found")
    
    booking_data = serialize_doc(booking_doc)
    
    # Update booking
    update_data = {"status": update.status}
    if update.admin_notes:
        update_data["admin_notes"] = update.admin_notes
    
    await db.bookings.update_one(
        {"id": booking_id},
        {"$set": update_data}
    )
    
    # Send email to user based on status
    if update.status in ["approved", "rejected"]:
        # Import datetime for email template
        from datetime import datetime

        # Get user details
        user_doc = await db.users.find_one({"id": booking_data["user_id"]})
        if not user_doc:
            logger.error(f"User not found for booking {booking_id}")
            return {"message": "Booking updated but email not sent"}
        
        user_data = serialize_doc(user_doc)
        
        # Get event details
        event_doc = await db.events.find_one({"id": booking_data["event_id"]})
        if not event_doc:
            logger.error(f"Event not found for booking {booking_id}")
            return {"message": "Booking updated but email not sent"}
        
        event_data = serialize_doc(event_doc)
        
        # Prepare email content
        email_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Vibrant Yoga - Booking {update.status.title()}</title>
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
                body {{
                    font-family: 'Poppins', Arial, sans-serif;
                    line-height: 1.6;
                    color: #4B5563;
                    margin: 0;
                    padding: 0;
                    background-color: #F9FAFB;
                }}
                .email-container {{
                    max-width: 600px;
                    margin: 0 auto;
                    background-color: #FFFFFF;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
                }}
                .email-header {{
                    background-color: #553C9A;
                    padding: 20px;
                    text-align: center;
                }}
                .email-header h1 {{
                    color: white;
                    margin: 0;
                    font-size: 24px;
                    font-weight: 600;
                }}
                .email-body {{
                    padding: 30px;
                }}
                .greeting {{
                    font-size: 18px;
                    font-weight: 500;
                    margin-bottom: 20px;
                }}
                .booking-status {{
                    font-size: 16px;
                    margin-bottom: 25px;
                }}
                .booking-status .status {{
                    display: inline-block;
                    padding: 6px 15px;
                    border-radius: 20px;
                    font-weight: 600;
                    color: white;
                    background-color: #F59E0B;
                    margin: 0 5px;
                }}
                .section-title {{
                    font-size: 16px;
                    font-weight: 600;
                    color: #553C9A;
                    margin-top: 25px;
                    margin-bottom: 10px;
                    border-bottom: 2px solid #F3F4F6;
                    padding-bottom: 8px;
                }}
                .details-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 25px;
                }}
                .details-table td {{
                    padding: 10px;
                    border: 1px solid #E5E7EB;
                }}
                .details-table .label {{
                    font-weight: 500;
                    background-color: #F9F7FF;
                    width: 35%;
                }}
                .details-table .value {{
                    width: 65%;
                }}
                .notice-box {{
                    background-color: #EEF2FF; /* Light blue background */
                    border-left: 4px solid #6366F1; /* Indigo border */
                    padding: 15px;
                    margin-top: 20px;
                    margin-bottom: 20px;
                    border-radius: 4px;
                }}
                .notice-box strong {{
                    color: #4F46E5; /* Indigo text for emphasis */
                }}
                .footer {{
                    background-color: #F9F7FF;
                    padding: 20px;
                    text-align: center;
                    font-size: 14px;
                }}
                .thank-you {{
                    font-size: 16px;
                    font-weight: 500;
                    color: #553C9A;
                    margin-top: 30px;
                    text-align: center;
                }}
                .contact-info {{
                    margin-top: 5px;
                    color: #6B7280;
                    font-size: 13px;
                    text-align: center;
                }}
                .cta-button {{
                    display: inline-block;
                    background-color: #553C9A;
                    color: white !important;
                    text-decoration: none;
                    padding: 12px 25px;
                    border-radius: 5px;
                    font-weight: 500;
                    margin-top: 20px;
                }}
                .button-container {{
                    text-align: center;
                    margin: 25px 0;
                }}
                .highlight {{
                    color: #553C9A;
                    font-weight: 600;
                }}
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="email-header">
                    <h1>Vibrant Yoga</h1>
                </div>
                <div class="email-body">
                    <div class="greeting">Dear {user_data['name']},</div>
                    <div class="booking-status">
                        Your booking for <strong>"{event_data['title']}"</strong> has been <span class="status">{update.status.title()}</span>.
                    </div>
                    
                    <div class="section-title">Event Details</div>
                    <table class="details-table">
                        <tr>
                            <td class="label">Event Name</td>
                            <td class="value">{event_data['title']}</td>
                        </tr>
                        <tr>
                            <td class="label">Date</td>
                            <td class="value">{event_data['date']}</td>
                        </tr>
                        <tr>
                            <td class="label">Time</td>
                            <td class="value">{event_data['time']} {event_data.get('timezone', '')}</td>
                        </tr>
                        <tr>
                            <td class="label">Event Type</td>
                            <td class="value">{event_data.get('event_type', 'N/A').title()}</td>
                        </tr>
                        <tr>
                            <td class="label">Amount</td>
                            <td class="value">₹{booking_data['amount']}</td>
                        </tr>
                    </table>
                    
                    <div class="notice-box">
                        <strong>Next Steps:</strong><br>
                        Please note that your booking has been {update.status.title()}. If you have any questions, please contact <NAME_EMAIL>
                    </div>
                    
                    <div class="button-container">
                        <a href="https://vibrantyoga.com/my-bookings" class="cta-button">Manage Your Booking</a>
                    </div>
                    
                    <div class="thank-you">Thank you for choosing Vibrant Yoga!</div>
                    <div class="contact-info">If you have any questions, please contact <NAME_EMAIL></div>
                </div>
                <div class="footer">
                    &copy; {datetime.now().year} Vibrant Yoga. All rights reserved.
                </div>
            </div>
        </body>
        </html>
        """

    
    # Initialize attachments as empty list
    attachments = []
    
    # Create PDF attachment for approved bookings
    if update.status == "approved":
        try:
            # Import required modules
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image as RLImage, Flowable
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import mm, inch
            from reportlab.lib import colors
            from reportlab.lib.colors import HexColor
            from reportlab.lib.enums import TA_CENTER, TA_RIGHT
            from io import BytesIO
            from pathlib import Path
            from datetime import datetime
            
            # Custom Watermark class for PDF
            class Watermark(Flowable):
                def __init__(self, text="Vibrant Yoga", opacity=0.1):
                    Flowable.__init__(self)
                    self.text = text
                    self.opacity = opacity
                    
                def draw(self):
                    self.canv.saveState()
                    self.canv.setFont("Helvetica-Bold", 80)
                    self.canv.setFillColor(colors.lightgrey)
                    self.canv.setFillAlpha(self.opacity)
                    self.canv.translate(A4[0]/2, A4[1]/3)
                    self.canv.rotate(30)
                    self.canv.drawCentredString(0, 0, self.text)
                    self.canv.restoreState()
            
            # Create PDF
            buffer = BytesIO()
            doc = SimpleDocTemplate(buffer, pagesize=A4,
                           rightMargin=72, leftMargin=72,
                           topMargin=72, bottomMargin=72)
            
            # Define styles
            ROOT_DIR = os.path.dirname(os.path.abspath(__file__))
            styles = getSampleStyleSheet()
            styles.add(ParagraphStyle(name='Centered', alignment=TA_CENTER, fontName='Helvetica-Bold', fontSize=14, textColor=HexColor('#553C9A')))  # Purple color for titles
            styles.add(ParagraphStyle(name='Right', alignment=TA_RIGHT))
            styles.add(ParagraphStyle(name='Subtitle', fontName='Helvetica-Bold', fontSize=12, textColor=HexColor('#553C9A')))  # Purple color for subtitles
            styles.add(ParagraphStyle(name='Footer', alignment=TA_CENTER, fontSize=8, textColor=colors.gray))
            
            # Create document content
            elements = []
            
            # Add watermark
            elements.append(Watermark())
            
            # Logo path
            logo_path = Path(ROOT_DIR).parent / 'frontend' / 'public' / 'vibrant-logo.png'
            
            # Add logo if it exists
            if logo_path.exists():
                logo = RLImage(str(logo_path), width=2*inch, height=1*inch)
                elements.append(logo)
            
            # Create a custom title style with purple color
            title_style = ParagraphStyle(
                'TitleStyle',
                parent=styles['Centered'],
                fontName='Helvetica-Bold',
                fontSize=16,
                textColor=HexColor('#553C9A')
            )
            
            # Add the title
            elements.append(Paragraph("Booking Confirmation", title_style))
            elements.append(Spacer(1, 0.2*inch))
            
            # Add participant details
            elements.append(Paragraph("Participant Details", styles['Subtitle']))
            elements.append(Spacer(1, 0.1*inch))
            
            participant_data = [
                ["Name", user_data.get('name', 'N/A')],
                ["Email", user_data.get('email', 'N/A')],
                ["Phone", user_data.get('phone', 'N/A')]
            ]
            
            participant_table = Table(participant_data, colWidths=[1.5*inch, 4*inch])
            participant_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), HexColor('#F9F7FF')),  # Light purple background for labels
                ('TEXTCOLOR', (0, 0), (0, -1), HexColor('#553C9A')),  # Purple text for labels
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (-1, -1), 'Helvetica'),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)
            ]))
            
            elements.append(participant_table)
            elements.append(Spacer(1, 0.3*inch))
            
            # Add event details
            elements.append(Paragraph("Event Details", styles['Subtitle']))
            elements.append(Spacer(1, 0.1*inch))
            
            event_data_table = [
                ["Event Name", event_data.get('title', 'N/A')],
                ["Date", event_data.get('date', 'N/A')],
                ["Time", f"{event_data.get('time', 'N/A')} {event_data.get('timezone', '')}"],
                ["Event Type", event_data.get('event_type', 'N/A').title()],
                ["Amount", f"₹{booking_data['amount']}"],
                ["Booking ID", booking_data.get('id', 'N/A')],
                ["Booking Status", update.status.upper()]
            ]
            
            event_table = Table(event_data_table, colWidths=[1.5*inch, 4*inch])
            event_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), HexColor('#F9F7FF')),  # Light purple background for labels
                ('TEXTCOLOR', (0, 0), (0, -1), HexColor('#553C9A')),  # Purple text for labels
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (-1, -1), 'Helvetica'),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
                # Highlight the status row
                ('BACKGROUND', (1, -1), (1, -1), HexColor('#10B981')),  # Green background for APPROVED status
                ('TEXTCOLOR', (1, -1), (1, -1), colors.white)  # White text for status
            ]))
            
            elements.append(event_table)
            elements.append(Spacer(1, 0.3*inch))
            
            # Add information about online sessions if applicable
            if event_data.get('is_online'):
                elements.append(Paragraph("Online Session Information", styles['Subtitle']))
                elements.append(Spacer(1, 0.1*inch))
                
                online_info = f"This is an online session. "
                if event_data.get('session_link'):
                    online_info += f"Please use the following link to join: {event_data.get('session_link')}"
                else:
                    online_info += "The session link will be shared with you before the event."
                    
                elements.append(Paragraph(online_info, styles['Normal']))
                elements.append(Spacer(1, 0.2*inch))
            
            # Add terms and conditions
            elements.append(Paragraph("Terms & Conditions", styles['Subtitle']))
            elements.append(Spacer(1, 0.1*inch))
            
            terms_data = [
                ["1.", "This confirmation is valid only for the date and time specified."],
                ["2.", "Please arrive at least 15 minutes before the session start time."],
                ["3.", "No refunds for cancellations made less than 24 hours before the event."],
                ["4.", "Vibrant Yoga reserves the right to make any changes to the session if required."]
            ]
            
            terms_table = Table(terms_data, colWidths=[0.3*inch, 5.2*inch])
            terms_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (-1, -1), 'Helvetica'),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6)
            ]))
            
            elements.append(terms_table)
            elements.append(Spacer(1, 0.3*inch))
            
            # Add thank you note with better styling
            thank_you_style = ParagraphStyle(
                'ThankYouStyle',
                parent=styles['Italic'],
                textColor=HexColor('#553C9A'),
                alignment=TA_CENTER,
                fontSize=10
            )
            elements.append(Paragraph("Thank you for choosing Vibrant Yoga!", thank_you_style))
            elements.append(Spacer(1, 0.1*inch))
            
            contact_style = ParagraphStyle(
                'ContactStyle',
                parent=styles['Normal'],
                alignment=TA_CENTER,
                fontSize=9,
                textColor=colors.gray
            )
            elements.append(Paragraph("If you have any questions, please contact <NAME_EMAIL>", contact_style))
            elements.append(Spacer(1, 0.1*inch))
            
            # Build PDF
            doc.build(elements)
            
            # Get PDF content and close buffer
            pdf_content = buffer.getvalue()
            buffer.close()
            
            # Create in-memory PDF attachment for email
            from fastapi import UploadFile
            from starlette.datastructures import Headers
            from io import BytesIO
            
            # Prepare filename
            pdf_filename = f"VibrantYoga_Booking_{booking_data['id']}.pdf"
            
            # Create BytesIO object with the PDF content
            pdf_io = BytesIO(pdf_content)
            pdf_io.seek(0)  # Important: reset position to start
            
            # Create UploadFile object which FastAPI-Mail can process
            upload_file = UploadFile(
                filename=pdf_filename,
                file=pdf_io,
                headers=Headers({"content-type": "application/pdf"})
            )
            
            # Add PDF to attachments
            attachments = [upload_file]
            print(f"Created in-memory PDF attachment: {pdf_filename}")
            
        except Exception as e:
            logger.error(f"Error generating PDF attachment: {str(e)}")
            logger.error("Continuing without PDF attachment")
    
    # Send email with attachments if any
    await send_email(
        to_email=user_data["email"],
        subject=f"Booking {update.status.title()} - Vibrant Yoga",
        body=email_body,
        attachments=attachments
    )
    
    return {"message": "Booking updated successfully"}

@api_router.post("/bookings/{booking_id}/payment-proof")
async def upload_payment_proof(
    booking_id: str,
    request: Request,
    current_user: dict = Depends(get_current_user)
):
    """Upload payment proof for booking (image or UTR number)"""
    try:
        form_data = await request.form()
        file = form_data.get("file")
        utr_number = form_data.get("utr_number")

        # Debug log to see what we're receiving
        logger.info(f"File type: {type(file)}, UTR type: {type(utr_number)}")
        if file:
            logger.info(f"File info: filename={getattr(file, 'filename', 'N/A')}")
        
        # Assume we have valid data by default and process with maximum leniency
        has_file = False
        has_utr = False
        
        # Check for UTR number first (simpler case)
        if utr_number is not None and str(utr_number).strip() != '':
            has_utr = True
            logger.info("Valid UTR number found")
        
        # Now check for file with various possible types
        if file is not None:
            # Try to access as UploadFile
            if hasattr(file, 'filename') and file.filename:
                has_file = True
                logger.info("Valid UploadFile detected")
            # It might be a string containing a filename
            elif isinstance(file, str) and file.strip():
                has_file = True  # Accept string filename too
                logger.info("File as string detected")
            # It could be some other non-empty representation
            elif file != "":
                has_file = True
                logger.info("Other non-empty file type detected")
        
        logger.info(f"Validation results: has_file={has_file}, has_utr={has_utr}")
        
        if not has_file and not has_utr:
            raise HTTPException(status_code=400, detail="Please provide either a payment proof image or a UTR number.")

        booking_doc = await db.bookings.find_one({
            "id": booking_id, 
            "user_id": current_user["id"]
        })
        if not booking_doc:
            raise HTTPException(status_code=404, detail="Booking not found")

        update_data = {}
        if has_file:
            try:
                # Different handling based on file type
                if hasattr(file, 'read') and callable(file.read):
                    # It's a proper UploadFile object
                    contents = await file.read()
                    if contents:  # Ensure file is not empty
                        payment_proof_base64 = convert_image_to_base64(contents)
                        update_data["payment_proof_base64"] = payment_proof_base64
                        logger.info("Successfully processed UploadFile")
                elif isinstance(file, bytes):
                    # It's already bytes
                    payment_proof_base64 = convert_image_to_base64(file)
                    update_data["payment_proof_base64"] = payment_proof_base64
                    logger.info("Successfully processed bytes data")
                else:
                    # Try to handle as string filename or other data
                    logger.info(f"Unable to process file of type {type(file)}, marking as submitted")
                    update_data["payment_proof_submitted"] = True
            except Exception as e:
                logger.error(f"Error processing file: {e}", exc_info=True)
                # Still mark as submitted even if we couldn't process the file
                update_data["payment_proof_submitted"] = True

        if has_utr:
            update_data["utr_number"] = utr_number.strip()

        if not update_data:
            # This case can happen if an empty file is uploaded and no UTR is given.
            raise HTTPException(status_code=400, detail="No valid data provided to update.")

        await db.bookings.update_one(
            {"id": booking_id},
            {"$set": update_data}
        )

        return {"message": "Payment proof submitted successfully"}
    except Exception as e:
        logger.error(f"Error in upload_payment_proof: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"Submission failed: {str(e)}")

@api_router.get("/bookings")
async def get_bookings(current_user: dict = Depends(get_current_user)):
    """Get user's bookings with user details for admin view"""
    if current_user["role"] == "admin":
        bookings_cursor = db.bookings.find({})
    else:
        bookings_cursor = db.bookings.find({"user_id": current_user["id"]})
    
    bookings_raw = await bookings_cursor.to_list(1000)
    bookings = []
    
    # Create a cache of user information to avoid multiple lookups
    user_cache = {}
    
    for booking_doc in bookings_raw:
        booking_data = serialize_doc(booking_doc)
        
        # For admin view, include user information
        if current_user["role"] == "admin":
            user_id = booking_data["user_id"]
            
            # Get user info from cache or from database if not in cache
            if user_id not in user_cache:
                user_doc = await db.users.find_one({"id": user_id})
                if user_doc:
                    user_cache[user_id] = {
                        "name": user_doc.get("name", "Unknown"),
                        "email": user_doc.get("email", "")
                    }
                else:
                    user_cache[user_id] = {"name": "Unknown User", "email": ""}
            
            # Add user details to booking data
            booking_data["user_name"] = user_cache[user_id]["name"]
            booking_data["user_email"] = user_cache[user_id]["email"]
        
        bookings.append(booking_data)
    
    return bookings

@api_router.put("/bookings/{booking_id}/status")
async def update_booking_status(
    booking_id: str,
    update: BookingUpdate,
    current_user: dict = Depends(get_admin_user)
):
    """Update booking status (admin only)"""
    if update.status not in ["pending", "approved", "rejected"]:
        raise HTTPException(status_code=400, detail="Invalid status")
    
    # Get booking details
    booking_doc = await db.bookings.find_one({"id": booking_id})
    if not booking_doc:
        raise HTTPException(status_code=404, detail="Booking not found")
    
    booking_data = serialize_doc(booking_doc)
    
    # Update booking
    update_data = {"status": update.status}
    if update.admin_notes:
        update_data["admin_notes"] = update.admin_notes
    
    await db.bookings.update_one(
        {"id": booking_id},
        {"$set": update_data}
    )
    
    # Send email to user based on status
    if update.status in ["approved", "rejected"]:

        # Get user details
        user_doc = await db.users.find_one({"id": booking_data["user_id"]})
        if not user_doc:
            logger.error(f"User not found for booking {booking_id}")
            return {"message": "Booking updated but email not sent"}
        
        user_data = serialize_doc(user_doc)
        
        # Get event details
        event_doc = await db.events.find_one({"id": booking_data["event_id"]})
        if not event_doc:
            logger.error(f"Event not found for booking {booking_id}")
            return {"message": "Booking updated but email not sent"}
        
        event_data = serialize_doc(event_doc)
        
        # Prepare email content
        email_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Vibrant Yoga - Booking {update.status.title()}</title>
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
                body {{
                    font-family: 'Poppins', Arial, sans-serif;
                    line-height: 1.6;
                    color: #4B5563;
                    margin: 0;
                    padding: 0;
                    background-color: #F9FAFB;
                }}
                .email-container {{
                    max-width: 600px;
                    margin: 0 auto;
                    background-color: #FFFFFF;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
                }}
                .email-header {{
                    background-color: #553C9A;
                    padding: 20px;
                    text-align: center;
                }}
                .email-header h1 {{
                    color: white;
                    margin: 0;
                    font-size: 24px;
                    font-weight: 600;
                }}
                .email-body {{
                    padding: 30px;
                }}
                .greeting {{
                    font-size: 18px;
                    font-weight: 500;
                    margin-bottom: 20px;
                }}
                .booking-status {{
                    font-size: 16px;
                    margin-bottom: 25px;
                }}
                .booking-status .status {{
                    display: inline-block;
                    padding: 6px 15px;
                    border-radius: 20px;
                    font-weight: 600;
                    color: white;
                    background-color: {{'#10B981' if update.status == 'approved' else '#F59E0B'}};
                    margin: 0 5px;
                }}
                .section-title {{
                    font-size: 16px;
                    font-weight: 600;
                    color: #553C9A;
                    margin-top: 25px;
                    margin-bottom: 10px;
                    border-bottom: 2px solid #F3F4F6;
                    padding-bottom: 8px;
                }}
                .details-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 25px;
                }}
                .details-table td {{
                    padding: 10px;
                    border: 1px solid #E5E7EB;
                }}
                .details-table .label {{
                    font-weight: 500;
                    background-color: #F9F7FF;
                    width: 35%;
                }}
                .details-table .value {{
                    width: 65%;
                }}
                .admin-notes {{
                    background-color: #FFFBEB;
                    border-left: 4px solid #F59E0B;
                    padding: 12px 15px;
                    margin-top: 20px;
                    margin-bottom: 20px;
                }}
                .footer {{
                    background-color: #F9F7FF;
                    padding: 20px;
                    text-align: center;
                    font-size: 14px;
                }}
                .thank-you {{
                    font-size: 16px;
                    font-weight: 500;
                    color: #553C9A;
                    margin-top: 30px;
                    text-align: center;
                }}
                .contact-info {{
                    margin-top: 5px;
                    color: #6B7280;
                    font-size: 13px;
                    text-align: center;
                }}
                .cta-button {{
                    display: inline-block;
                    background-color: #553C9A;
                    color: white !important;
                    text-decoration: none;
                    padding: 12px 25px;
                    border-radius: 5px;
                    font-weight: 500;
                    margin-top: 20px;
                }}
                .button-container {{
                    text-align: center;
                    margin: 25px 0;
                }}
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="email-header">
                    <h1>Vibrant Yoga</h1>
                </div>
                <div class="email-body">
                    <div class="greeting">Dear {user_data['name']},</div>
                    <div class="booking-status">
                        Your booking for <strong>"{event_data['title']}"</strong> has been <span class="status">{update.status}</span>
                    </div>
                    
                    <div class="section-title">Event Details</div>
                    <table class="details-table">
                        <tr>
                            <td class="label">Event Name</td>
                            <td class="value">{event_data['title']}</td>
                        </tr>
                        <tr>
                            <td class="label">Date</td>
                            <td class="value">{event_data['date']}</td>
                        </tr>
                        <tr>
                            <td class="label">Time</td>
                            <td class="value">{event_data['time']} {event_data.get('timezone', '')}</td>
                        </tr>
                        <tr>
                            <td class="label">Event Type</td>
                            <td class="value">{event_data.get('event_type', 'N/A').title()}</td>
                        </tr>
                        <tr>
                            <td class="label">Amount</td>
                            <td class="value">₹{booking_data['amount']}</td>
                        </tr>
                    </table>
                    
                    {f'<div class="admin-notes"><strong>Admin Notes:</strong><br>{update.admin_notes}</div>' if update.admin_notes else ''}
                    
                    <div class="button-container">
                        <a href="https://vibrantyoga.com/bookings/{booking_data['id']}" class="cta-button">View Booking Details</a>
                    </div>
                    
                    <div class="thank-you">Thank you for choosing Vibrant Yoga!</div>
                    <div class="contact-info">If you have any questions, please contact <NAME_EMAIL></div>
                </div>
                <div class="footer">
                    &copy; {datetime.now().year} Vibrant Yoga. All rights reserved.
                </div>
            </div>
        </body>
        </html>
        """

        # Add attachments if booking is approved
        attachments = None
        if update.status == "approved":
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import mm, inch
            from reportlab.lib import colors
            from io import BytesIO
            from datetime import datetime
            
            # Custom Watermark class for PDF
            class Watermark(Flowable):
                def __init__(self, text="Vibrant Yoga", opacity=0.1):
                    Flowable.__init__(self)
                    self.text = text
                    self.opacity = opacity
                    
                def draw(self):
                    self.canv.saveState()
                    self.canv.setFont("Helvetica-Bold", 80)
                    self.canv.setFillColor(colors.lightgrey)
                    self.canv.setFillAlpha(self.opacity)
                    self.canv.translate(A4[0]/2, A4[1]/3)
                    self.canv.rotate(30)
                    self.canv.drawCentredString(0, 0, self.text)
                    self.canv.restoreState()
            
            # Create PDF
            buffer = BytesIO()
            doc = SimpleDocTemplate(buffer, pagesize=A4,
                               rightMargin=72, leftMargin=72,
                               topMargin=72, bottomMargin=72)
            
            # Define styles
            styles = getSampleStyleSheet()
            styles.add(ParagraphStyle(name='Centered', alignment=TA_CENTER, fontName='Helvetica-Bold', fontSize=14, textColor=HexColor('#553C9A')))  # Purple color for titles
            styles.add(ParagraphStyle(name='Right', alignment=TA_RIGHT))
            styles.add(ParagraphStyle(name='Subtitle', fontName='Helvetica-Bold', fontSize=12, textColor=HexColor('#553C9A')))  # Purple color for subtitles
            styles.add(ParagraphStyle(name='Footer', alignment=TA_CENTER, fontSize=8, textColor=colors.gray))
            
            # Create document content
            elements = []
            
            # Add watermark
            elements.append(Watermark())
            
            # Logo path
            logo_path = Path(ROOT_DIR).parent / 'frontend' / 'public' / 'vibrant-logo.png'
            
            # Add logo if it exists
            if logo_path.exists():
                logo = RLImage(str(logo_path), width=2*inch, height=1*inch)
                elements.append(logo)
            
            # Create a custom title style with purple color
            title_style = ParagraphStyle(
                'TitleStyle',
                parent=styles['Centered'],
                fontName='Helvetica-Bold',
                fontSize=16,
                textColor=HexColor('#553C9A')
            )
            
            # Add the title
            elements.append(Paragraph("BOOKING CONFIRMATION", title_style))
            elements.append(Spacer(1, 0.3*inch))
            
            # Create custom line class for separator instead of HTML
            class HorizontalLine(Flowable):
                def __init__(self, width, height=0, color=HexColor('#553C9A')):
                    Flowable.__init__(self)
                    self.width = width
                    self.height = height
                    self.color = color
                    
                def draw(self):
                    self.canv.setStrokeColor(self.color)
                    self.canv.setLineWidth(2)
                    self.canv.line(0, 0, self.width, 0)
            
            # Add separator
            elements.append(HorizontalLine(450))  # Width set to 450 points
            elements.append(Spacer(1, 0.3*inch))
            
            # Booking reference style
            ref_label_style = ParagraphStyle(
                'RefLabelStyle',
                parent=styles['Subtitle'],
                fontName='Helvetica-Bold',
                textColor=HexColor('#553C9A')
            )
            
            ref_value_style = ParagraphStyle(
                'RefValueStyle',
                parent=styles['Normal']
            )
            
            date_style = ParagraphStyle(
                'DateStyle',
                parent=styles['Normal']
            )
            
            # Status styles
            approved_style = ParagraphStyle(
                'ApprovedStyle',
                parent=styles['Normal'],
                textColor=HexColor('#10B981')  # Green for approved
            )
            
            # Booking Details with proper styling
            elements.append(Paragraph("Booking Reference:", ref_label_style))
            elements.append(Paragraph(booking_data['id'], ref_value_style))
            elements.append(Spacer(1, 0.1*inch))
            
            elements.append(Paragraph("Date:", ref_label_style))
            # Format date with timezone information
            current_time = datetime.now()
            timezone_offset = current_time.astimezone().strftime('%z')
            formatted_timezone = f"UTC{timezone_offset[:3]}:{timezone_offset[3:]}" if timezone_offset else ""
            elements.append(Paragraph(f"{current_time.strftime('%d %B, %Y %H:%M')} {formatted_timezone}", date_style))
            elements.append(Spacer(1, 0.1*inch))
            
            elements.append(Paragraph("Status:", ref_label_style))
            elements.append(Paragraph(update.status.upper(), approved_style))
            elements.append(Spacer(1, 0.3*inch))
            
            # Create styled subtitle for sections
            section_title_style = ParagraphStyle(
                'SectionTitleStyle',
                parent=styles['Subtitle'],
                fontName='Helvetica-Bold',
                textColor=HexColor('#553C9A'),
                alignment=TA_LEFT
            )
            
            # Event Details styled header
            elements.append(Paragraph("EVENT DETAILS", section_title_style))
            elements.append(Spacer(1, 0.15*inch))
            
            # Create paragraph styles for table cells
            label_style = ParagraphStyle(
                'LabelStyle',
                parent=styles['Normal'],
                fontName='Helvetica-Bold',
                textColor=HexColor('#553C9A')  # Purple color for labels
            )
            
            value_style = ParagraphStyle(
                'ValueStyle',
                parent=styles['Normal']
            )
            
            online_style = ParagraphStyle(
                'OnlineStyle',
                parent=styles['Normal'],
                textColor=HexColor('#3B82F6')  # Blue for online mode
            )
            
            inperson_style = ParagraphStyle(
                'InPersonStyle',
                parent=styles['Normal'],
                textColor=HexColor('#10B981')  # Green for in-person mode
            )
            
            # Create table with properly styled paragraphs
            event_table_data = [
                [Paragraph("Event Name", label_style), Paragraph(event_data['title'], value_style)],
                [Paragraph("Date", label_style), Paragraph(event_data['date'], value_style)],
                # Include the timezone with the time (if available)
                [Paragraph("Time", label_style), Paragraph(
                    f"{event_data['time']} {event_data.get('timezone', '')}", 
                    value_style
                )],
                [Paragraph("Event Type", label_style), Paragraph(event_data.get('event_type', 'N/A').title(), value_style)],
            ]
            
            if event_data.get('is_online'):
                event_table_data.append([Paragraph("Mode", label_style), Paragraph("Online", online_style)])
                if event_data.get('session_link'):
                    event_table_data.append([Paragraph("Session Link", label_style), Paragraph(event_data['session_link'], online_style)])
            else:
                event_table_data.append([Paragraph("Mode", label_style), Paragraph("In-person", inperson_style)])
                if event_data.get('venue'):
                    event_table_data.append([Paragraph("Venue", label_style), Paragraph(event_data['venue'], value_style)])
            
            event_table = Table(event_table_data, colWidths=[2*inch, 3.5*inch])
            event_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), HexColor('#F3F4F6')),  # Light gray background for first column
                ('TEXTCOLOR', (0, 0), (0, -1), HexColor('#553C9A')),  # Purple color for labels
                ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),  # More padding for better readability
                ('TOPPADDING', (0, 0), (-1, -1), 10),     # More padding for better readability
                ('BACKGROUND', (1, 0), (-1, -1), colors.white),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),   # Vertically center content
                ('GRID', (0, 0), (-1, -1), 0.5, HexColor('#E5E7EB'))  # Subtle grid lines
            ]))
            elements.append(event_table)
            elements.append(Spacer(1, 0.3*inch))
            
            # Payment Details - styled header using section title style
            elements.append(Paragraph("PAYMENT DETAILS", section_title_style))
            elements.append(Spacer(1, 0.15*inch))
            
            # Create payment status styles
            paid_style = ParagraphStyle(
                'PaidStyle',
                parent=styles['Normal'],
                fontName='Helvetica-Bold',
                textColor=HexColor('#10B981')  # Green for paid status
            )
            
            amount_style = ParagraphStyle(
                'AmountStyle',
                parent=styles['Normal'],
                fontName='Helvetica-Bold'
            )
            
            payment_data = [
                [Paragraph("Amount", label_style), Paragraph(f"₹{booking_data['amount']}", amount_style)],
                [Paragraph("Payment Status", label_style), 
                 Paragraph("Paid", paid_style)],
            ]
            
            if booking_data.get('utr_number'):
                payment_data.append([Paragraph("UTR Number", label_style), Paragraph(booking_data['utr_number'], value_style)])
            
            payment_table = Table(payment_data, colWidths=[2*inch, 3.5*inch])
            payment_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), HexColor('#F3F4F6')),  # Light gray background for first column
                ('TEXTCOLOR', (0, 0), (0, -1), HexColor('#553C9A')),  # Purple color for labels
                ('ALIGN', (0, 0), (0, -1), 'LEFT'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 10),  # More padding for better readability
                ('TOPPADDING', (0, 0), (-1, -1), 10),     # More padding for better readability
                ('BACKGROUND', (1, 0), (-1, -1), colors.white),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),   # Vertically center content
                ('GRID', (0, 0), (-1, -1), 0.5, HexColor('#E5E7EB'))  # Subtle grid lines
            ]))
            elements.append(payment_table)
            elements.append(Spacer(1, 0.3*inch))
            
            # Terms & Conditions with better styling
            elements.append(Paragraph("TERMS & CONDITIONS", section_title_style))
            elements.append(Spacer(1, 0.2*inch))
            
            # Define a custom style for terms with a light purple background
            terms_style = ParagraphStyle(
                'TermsStyle',
                parent=styles['Normal'],
                fontSize=9,
                leftIndent=15,
                rightIndent=15,
                spaceAfter=5,
                textColor=HexColor('#4B5563'),  # Darker gray for better readability
                backColor=HexColor('#F9F7FF')   # Very light purple background
            )
            
            # Terms with modern styling
            terms = [
                "1. Please arrive at least 15 minutes before the class start time.",
                "2. Bring your own yoga mat and water bottle.",
                "3. Wear comfortable clothing suitable for yoga practice.",
                "4. Please present this confirmation (digital or printed) at the reception.",
                "5. Cancellations must be made at least 24 hours before the scheduled class.",
            ]
            
            # Create a terms container with background
            terms_table_data = [[Paragraph(term, terms_style)] for term in terms]
            terms_table = Table(terms_table_data, colWidths=[5.5*inch])
            terms_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, -1), HexColor('#F9F7FF')),  # Light purple background
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('TOPPADDING', (0, 0), (-1, -1), 8),
                ('LEFTPADDING', (0, 0), (-1, -1), 15), 
                ('RIGHTPADDING', (0, 0), (-1, -1), 15),
                ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ('BOX', (0, 0), (-1, -1), 0.5, HexColor('#E9ECEF')),  # Subtle border
            ]))
            
            elements.append(terms_table)
            elements.append(Spacer(1, 0.3*inch))
            
            # Add thank you note with better styling
            thank_you_style = ParagraphStyle(
                'ThankYouStyle',
                parent=styles['Italic'],
                textColor=HexColor('#553C9A'),
                alignment=TA_CENTER,
                fontSize=10
            )
            elements.append(Paragraph("Thank you for choosing Vibrant Yoga!", thank_you_style))
            elements.append(Spacer(1, 0.1*inch))
            
            contact_style = ParagraphStyle(
                'ContactStyle',
                parent=styles['Normal'],
                alignment=TA_CENTER,
                fontSize=9,
                textColor=colors.gray
            )
            elements.append(Paragraph("If you have any questions, please contact <NAME_EMAIL>", contact_style))
            elements.append(Spacer(1, 0.1*inch))
            
            # Build PDF
            doc.build(elements)
            
            # Get PDF content and close buffer
            pdf_content = buffer.getvalue()
            buffer.close()
            
            # Create in-memory PDF attachment for FastAPI-Mail
            from fastapi import UploadFile
            from starlette.datastructures import Headers
            from io import BytesIO
            
            # Prepare filename
            pdf_filename = f"VibrantYoga_Booking_{booking_data['id']}.pdf"
            
            # Create BytesIO object with the PDF content
            pdf_io = BytesIO(pdf_content)
            pdf_io.seek(0)  # Important: reset position to start
            
            # Create UploadFile object which FastAPI-Mail can process
            upload_file = UploadFile(
                filename=pdf_filename,
                file=pdf_io,
                headers=Headers({"content-type": "application/pdf"})
            )
            
            # Create a single attachment using the UploadFile object
            attachments = [upload_file]
            
            print(f"Created in-memory PDF attachment: {pdf_filename}")
            
        
        # Send email with attachments if any
        await send_email(
            to_email=user_data["email"],
            subject=f"Booking {update.status.title()} - Vibrant Yoga",
            body=email_body,
            attachments=attachments
        )
    
    return {"message": "Booking status updated successfully"}


@api_router.delete("/bookings/{booking_id}")
async def delete_booking(
    booking_id: str,
    current_user: dict = Depends(get_admin_user)
):
    """Delete a booking (admin only)"""
    # Check if booking exists
    logging.info(f"Attempting to delete booking with id: {booking_id}")
    booking_doc = await db.bookings.find_one({"id": booking_id})
    
    if not booking_doc:
        logging.error(f"Booking not found with id: {booking_id}")
        raise HTTPException(status_code=404, detail="Booking not found")
    
    # Delete the booking
    result = await db.bookings.delete_one({"id": booking_id})
    
    if result.deleted_count == 0:
        logging.error(f"Failed to delete booking with id: {booking_id}")
        raise HTTPException(status_code=500, detail="Failed to delete booking")
    
    logging.info(f"Successfully deleted booking with id: {booking_id}")
    return {"message": "Booking deleted successfully"}

# PDF Generation endpoint
@api_router.get("/bookings/{booking_id}/download-pdf")
async def download_booking_pdf(
    booking_id: str,
    token: str = None,
    current_user: dict = Depends(get_current_user_or_none)
):
    # If token is provided in query params, validate it
    if token and not current_user:
        try:
            logging.info(f"Attempting to validate token for PDF download: {booking_id}")
            # First try decoding with standard JWT_SECRET
            try:
                payload = jwt.decode(token, JWT_SECRET, algorithms=["HS256"])
            except:
                # If that fails, try decoding with other common options
                try:
                    payload = jwt.decode(token, JWT_SECRET, algorithms=["HS256"], options={"verify_signature": False})
                    logging.info("Token decoded with signature verification disabled")
                except Exception as e:
                    logging.error(f"Failed to decode token: {str(e)}")
                    raise HTTPException(status_code=401, detail="Invalid token format")
            
            # Log the full payload for debugging
            logging.info(f"Token payload: {payload}")
            
            # Try different payload key patterns
            user_id = payload.get("user_id") or payload.get("sub") or payload.get("id")
            logging.info(f"Token decoded, extracted user_id: {user_id}")
            
            if not user_id:
                logging.error("No user_id found in token payload")
                raise HTTPException(status_code=401, detail="Invalid token payload")
                
            current_user = await db.users.find_one({"id": user_id})
            if not current_user:
                logging.error(f"User not found with id: {user_id}")
                raise HTTPException(status_code=401, detail="User not found")
                
            current_user = serialize_doc(current_user)
            logging.info(f"User authenticated for PDF download: {current_user['email']}")
        except jwt.PyJWTError as e:
            logging.error(f"JWT token error: {str(e)}")
            raise HTTPException(status_code=401, detail="Invalid token")
    
    if not current_user:
        raise HTTPException(status_code=401, detail="Authentication required")
    """Generate and download a PDF for a booking"""
    # Get booking details
    booking_doc = await db.bookings.find_one({"id": booking_id})
    if not booking_doc:
        raise HTTPException(status_code=404, detail="Booking not found")
    
    booking_data = serialize_doc(booking_doc)
    
    # Check if user has access to this booking
    if current_user["role"] != "admin" and current_user["id"] != booking_data["user_id"]:
        raise HTTPException(status_code=403, detail="You don't have permission to access this booking")
    
    # Get event details
    event_doc = await db.events.find_one({"id": booking_data["event_id"]})
    if not event_doc:
        raise HTTPException(status_code=404, detail="Event not found")
    
    event_data = serialize_doc(event_doc)
    
    # Custom Watermark class for PDF
    class Watermark(Flowable):
        def __init__(self, text="Vibrant Yoga", opacity=0.1):
            Flowable.__init__(self)
            self.text = text
            self.opacity = opacity
            
        def draw(self):
            self.canv.saveState()
            self.canv.setFont("Helvetica-Bold", 80)
            self.canv.setFillColor(colors.lightgrey)
            self.canv.setFillAlpha(self.opacity)
            self.canv.translate(A4[0]/2, A4[1]/3)
            self.canv.rotate(30)
            self.canv.drawCentredString(0, 0, self.text)
            self.canv.restoreState()
    
    # Create PDF
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=A4,
                          rightMargin=72, leftMargin=72,
                          topMargin=72, bottomMargin=72)
    
    # Define styles
    styles = getSampleStyleSheet()
    styles.add(ParagraphStyle(name='Centered', alignment=TA_CENTER, fontName='Helvetica-Bold', fontSize=14, textColor=HexColor('#553C9A')))  # Purple color for titles
    styles.add(ParagraphStyle(name='Right', alignment=TA_RIGHT))
    styles.add(ParagraphStyle(name='Subtitle', fontName='Helvetica-Bold', fontSize=12, textColor=HexColor('#553C9A')))  # Purple color for subtitles
    styles.add(ParagraphStyle(name='Footer', alignment=TA_CENTER, fontSize=8, textColor=colors.gray))
    
    # Create document content
    elements = []
    
    # Add watermark
    elements.append(Watermark())
    
    # Logo path
    logo_path = Path(ROOT_DIR).parent / 'frontend' / 'public' / 'vibrant-logo.png'
    
    # Add logo if it exists
    if logo_path.exists():
        logo = RLImage(str(logo_path), width=2*inch, height=1*inch)
        elements.append(logo)
    
    # Create a custom title style with purple color
    title_style = ParagraphStyle(
        'TitleStyle',
        parent=styles['Centered'],
        fontName='Helvetica-Bold',
        fontSize=16,
        textColor=HexColor('#553C9A')
    )
    
    # Add the title
    elements.append(Paragraph("BOOKING CONFIRMATION", title_style))
    elements.append(Spacer(1, 0.3*inch))
    
    # Create custom line class for separator instead of HTML
    class HorizontalLine(Flowable):
        def __init__(self, width, height=0, color=HexColor('#553C9A')):
            Flowable.__init__(self)
            self.width = width
            self.height = height
            self.color = color
            
        def draw(self):
            self.canv.setStrokeColor(self.color)
            self.canv.setLineWidth(2)
            self.canv.line(0, 0, self.width, 0)
    
    # Add separator
    elements.append(HorizontalLine(450))  # Width set to 450 points
    elements.append(Spacer(1, 0.3*inch))
    
    # Booking reference style
    ref_label_style = ParagraphStyle(
        'RefLabelStyle',
        parent=styles['Subtitle'],
        fontName='Helvetica-Bold',
        textColor=HexColor('#553C9A')
    )
    
    ref_value_style = ParagraphStyle(
        'RefValueStyle',
        parent=styles['Normal']
    )
    
    date_style = ParagraphStyle(
        'DateStyle',
        parent=styles['Normal']
    )
    
    # Status styles
    approved_style = ParagraphStyle(
        'ApprovedStyle',
        parent=styles['Normal'],
        textColor=HexColor('#10B981')  # Green for approved
    )
    
    pending_style = ParagraphStyle(
        'PendingStyle',
        parent=styles['Normal'],
        textColor=HexColor('#F59E0B')  # Amber for pending
    )
    
    # Booking Details with proper styling
    elements.append(Paragraph("Booking Reference:", ref_label_style))
    elements.append(Paragraph(booking_data['id'], ref_value_style))
    elements.append(Spacer(1, 0.1*inch))
    
    elements.append(Paragraph("Date:", ref_label_style))
    # Format date with timezone information
    current_time = datetime.now()
    timezone_offset = current_time.astimezone().strftime('%z')
    formatted_timezone = f"UTC{timezone_offset[:3]}:{timezone_offset[3:]}" if timezone_offset else ""
    elements.append(Paragraph(f"{current_time.strftime('%d %B, %Y %H:%M')} {formatted_timezone}", date_style))
    elements.append(Spacer(1, 0.1*inch))
    
    elements.append(Paragraph("Status:", ref_label_style))
    if booking_data['status'] == "approved":
        elements.append(Paragraph(booking_data['status'].upper(), approved_style))
    else:
        elements.append(Paragraph(booking_data['status'].upper(), pending_style))
    elements.append(Spacer(1, 0.3*inch))
    
    # Create styled subtitle for sections
    section_title_style = ParagraphStyle(
        'SectionTitleStyle',
        parent=styles['Subtitle'],
        fontName='Helvetica-Bold',
        textColor=HexColor('#553C9A'),
        alignment=TA_LEFT
    )
    
    # Event Details styled header
    elements.append(Paragraph("EVENT DETAILS", section_title_style))
    elements.append(Spacer(1, 0.15*inch))
    # Create paragraph styles for table cells
    label_style = ParagraphStyle(
        'LabelStyle',
        parent=styles['Normal'],
        fontName='Helvetica-Bold',
        textColor=HexColor('#553C9A')  # Purple color for labels
    )
    
    value_style = ParagraphStyle(
        'ValueStyle',
        parent=styles['Normal']
    )
    
    online_style = ParagraphStyle(
        'OnlineStyle',
        parent=styles['Normal'],
        textColor=HexColor('#3B82F6')  # Blue for online mode
    )
    
    inperson_style = ParagraphStyle(
        'InPersonStyle',
        parent=styles['Normal'],
        textColor=HexColor('#10B981')  # Green for in-person mode
    )
    
    # Create table with properly styled paragraphs
    event_table_data = [
        [Paragraph("Event Name", label_style), Paragraph(event_data['title'], value_style)],
        [Paragraph("Date", label_style), Paragraph(event_data['date'], value_style)],
        # Include the timezone with the time (if available)
        [Paragraph("Time", label_style), Paragraph(
            f"{event_data['time']} {event_data.get('timezone', '')}", 
            value_style
        )],
        [Paragraph("Event Type", label_style), Paragraph(event_data.get('event_type', 'N/A').title(), value_style)],
    ]
    
    if 'description' in event_data and event_data['description']:
        event_table_data.append([Paragraph("Description", label_style), Paragraph(event_data['description'], value_style)])
    
    if event_data.get('is_online'):
        event_table_data.append([Paragraph("Mode", label_style), Paragraph("Online", online_style)])
        if event_data.get('session_link'):
            event_table_data.append([Paragraph("Session Link", label_style), Paragraph(event_data['session_link'], online_style)])
    else:
        event_table_data.append([Paragraph("Mode", label_style), Paragraph("In-person", inperson_style)])
        if event_data.get('venue'):
            event_table_data.append([Paragraph("Venue", label_style), Paragraph(event_data['venue'], value_style)])
    
    event_table = Table(event_table_data, colWidths=[2*inch, 3.5*inch])
    event_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), HexColor('#F3F4F6')),  # Light gray background for first column
        ('TEXTCOLOR', (0, 0), (0, -1), HexColor('#553C9A')),  # Purple color for labels
        ('ALIGN', (0, 0), (0, -1), 'LEFT'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 10),  # More padding for better readability
        ('TOPPADDING', (0, 0), (-1, -1), 10),     # More padding for better readability
        ('BACKGROUND', (1, 0), (-1, -1), colors.white),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),   # Vertically center content
        ('GRID', (0, 0), (-1, -1), 0.5, HexColor('#E5E7EB'))  # Subtle grid lines
    ]))
    elements.append(event_table)
    elements.append(Spacer(1, 0.3*inch))
    
    # Payment Details - styled header using section title style
    elements.append(Paragraph("PAYMENT DETAILS", section_title_style))
    elements.append(Spacer(1, 0.15*inch))
    
    # Create payment status styles
    paid_style = ParagraphStyle(
        'PaidStyle',
        parent=styles['Normal'],
        fontName='Helvetica-Bold',
        textColor=HexColor('#10B981')  # Green for paid status
    )
    
    pending_style = ParagraphStyle(
        'PendingStyle',
        parent=styles['Normal'],
        fontName='Helvetica-Bold',
        textColor=HexColor('#F59E0B')  # Amber for pending status
    )
    
    amount_style = ParagraphStyle(
        'AmountStyle',
        parent=styles['Normal'],
        fontName='Helvetica-Bold'
    )
    
    payment_data = [
        [Paragraph("Amount", label_style), Paragraph(f"₹{booking_data['amount']}", amount_style)],
        [Paragraph("Payment Status", label_style), 
         Paragraph("Paid", paid_style) if booking_data['status'] == "approved" else Paragraph("Pending", pending_style)],
    ]
    
    if booking_data.get('utr_number'):
        payment_data.append([Paragraph("UTR Number", label_style), Paragraph(booking_data['utr_number'], value_style)])
    
    payment_table = Table(payment_data, colWidths=[2*inch, 3.5*inch])
    payment_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), HexColor('#F3F4F6')),  # Light gray background for first column
        ('TEXTCOLOR', (0, 0), (0, -1), HexColor('#553C9A')),  # Purple color for labels
        ('ALIGN', (0, 0), (0, -1), 'LEFT'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 10),  # More padding for better readability
        ('TOPPADDING', (0, 0), (-1, -1), 10),     # More padding for better readability
        ('BACKGROUND', (1, 0), (-1, -1), colors.white),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),   # Vertically center content
        ('GRID', (0, 0), (-1, -1), 0.5, HexColor('#E5E7EB'))  # Subtle grid lines
    ]))
    elements.append(payment_table)
    elements.append(Spacer(1, 0.3*inch))
    
    # Add a decorative separator
    elements.append(Spacer(1, 0.2*inch))
    elements.append(HorizontalLine(450, color=HexColor('#553C9A')))  # Using the custom HorizontalLine class
    elements.append(Spacer(1, 0.2*inch))
    
    # Terms & Conditions with better styling
    elements.append(Paragraph("TERMS & CONDITIONS", section_title_style))
    elements.append(Spacer(1, 0.2*inch))
    
    # Define a custom style for terms with a light purple background
    terms_style = ParagraphStyle(
        'TermsStyle',
        parent=styles['Normal'],
        fontSize=9,
        leftIndent=15,
        rightIndent=15,
        spaceAfter=5,
        textColor=HexColor('#4B5563'),  # Darker gray for better readability
        backColor=HexColor('#F9F7FF')   # Very light purple background
    )
    
    # Terms with modern styling
    terms = [
        "1. Please arrive at least 15 minutes before the class start time.",
        "2. Bring your own yoga mat and water bottle.",
        "3. Wear comfortable clothing suitable for yoga practice.",
        "4. Please present this confirmation (digital or printed) at the reception.",
        "5. Cancellations must be made at least 24 hours before the scheduled class.",
    ]
    
    # Create a terms container with background
    terms_table_data = [[Paragraph(term, terms_style)] for term in terms]
    terms_table = Table(terms_table_data, colWidths=[5.5*inch])
    terms_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, -1), HexColor('#F9F7FF')),  # Light purple background
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('TOPPADDING', (0, 0), (-1, -1), 8),
        ('LEFTPADDING', (0, 0), (-1, -1), 15), 
        ('RIGHTPADDING', (0, 0), (-1, -1), 15),
        ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ('BOX', (0, 0), (-1, -1), 0.5, HexColor('#E9ECEF')),  # Subtle border
    ]))
    
    elements.append(terms_table)
    elements.append(Spacer(1, 0.5*inch))
    
    # Define a footer separator with lighter color
    class FooterSeparator(Flowable):
        def __init__(self, width=300, height=0, color=HexColor('#E5E7EB')):
            Flowable.__init__(self)
            self.width = width
            self.height = height
            self.color = color
            
        def draw(self):
            self.canv.setStrokeColor(self.color)
            self.canv.setLineWidth(1)
            self.canv.line(0, 0, self.width, 0)
    
    # Add the footer separator (centered)
    elements.append(Spacer(1, 0.3*inch))
    elements.append(FooterSeparator())
    elements.append(Spacer(1, 0.2*inch))
    
    # Footer with better styling
    footer_style = ParagraphStyle(
        'FooterStyle', 
        parent=styles['Normal'],
        alignment=TA_CENTER,
        fontSize=9,
        textColor=HexColor('#6B7280')  # Medium gray for footer
    )
    
    # Colored thank you text
    thank_you_style = ParagraphStyle(
        'ThankYouStyle',
        parent=footer_style,
        textColor=HexColor('#553C9A'),
        fontName='Helvetica-Bold'
    )
    
    # Email style with blue color
    email_style = ParagraphStyle(
        'EmailStyle',
        parent=footer_style,
        textColor=HexColor('#1D4ED8'),
        alignment=TA_CENTER
    )
    
    elements.append(Paragraph("Thank you for choosing Vibrant Yoga!", thank_you_style))
    elements.append(Spacer(1, 0.1*inch))
    elements.append(Paragraph("For any queries, please contact us at:", footer_style))
    elements.append(Paragraph("<EMAIL>", email_style))
    elements.append(Paragraph(f"© {datetime.now().year} Vibrant Yoga. All Rights Reserved.", footer_style))
    
    # Build PDF
    doc.build(elements)
    buffer.seek(0)
    
    # Return PDF as StreamingResponse
    return StreamingResponse(
        buffer, 
        media_type="application/pdf",
        headers={"Content-Disposition": f"attachment; filename=booking-{booking_id}.pdf"}
    )

# Admin Dashboard Routes
@api_router.get("/admin/dashboard")
async def get_admin_dashboard(current_user: dict = Depends(get_admin_user)):
    """Get admin dashboard data"""
    total_users = await db.users.count_documents({})
    total_events = await db.events.count_documents({})
    total_bookings = await db.bookings.count_documents({})
    pending_bookings = await db.bookings.count_documents({"status": "pending"})
    approved_bookings = await db.bookings.count_documents({"status": "approved"})
    
    # Calculate revenue
    revenue_cursor = db.bookings.aggregate([
        {"$match": {"status": "approved"}},
        {"$group": {"_id": None, "total_revenue": {"$sum": "$amount"}}}
    ])
    revenue_result = await revenue_cursor.to_list(1)
    total_revenue = revenue_result[0]["total_revenue"] if revenue_result else 0
    
    # Get recent bookings
    recent_bookings_cursor = db.bookings.find().sort("created_at", -1).limit(10)
    recent_bookings_raw = await recent_bookings_cursor.to_list(10)
    recent_bookings = []
    
    for booking in recent_bookings_raw:
        recent_bookings.append(serialize_doc(booking))
    
    return {
        "total_users": total_users,
        "total_events": total_events,
        "total_bookings": total_bookings,
        "pending_bookings": pending_bookings,
        "approved_bookings": approved_bookings,
        "total_revenue": total_revenue,
        "recent_bookings": recent_bookings
    }

# Testimonials Routes
@api_router.get("/testimonials", response_model=List[Testimonial])
async def get_testimonials():
    """Get all testimonials"""
    testimonials_cursor = db.testimonials.find()
    testimonials = await testimonials_cursor.to_list(length=100)
    return [serialize_doc(t) for t in testimonials]

@api_router.post("/testimonials", response_model=Testimonial, status_code=status.HTTP_201_CREATED)
async def create_testimonial(
    name: str = Form(...),
    role: str = Form(...),
    content: str = Form(...),
    rating: int = Form(...),
    avatar: Optional[UploadFile] = File(None),
    current_user: dict = Depends(get_admin_user)
):
    """Create a new testimonial with avatar upload (admin only)"""
    testimonial_data = {
        "name": name,
        "role": role,
        "content": content,
        "rating": rating,
        "avatar_path": None
    }

    if avatar:
        file_location = f"uploads/{uuid.uuid4()}_{avatar.filename}"
        with open(file_location, "wb+") as file_object:
            file_object.write(avatar.file.read())
        testimonial_data["avatar_path"] = file_location

    result = await db.testimonials.insert_one(testimonial_data)
    created_testimonial = await db.testimonials.find_one({"_id": result.inserted_id})
    return serialize_doc(created_testimonial)

@api_router.put("/testimonials/{testimonial_id}", response_model=Testimonial)
async def update_testimonial(
    testimonial_id: str,
    name: str = Form(...),
    role: str = Form(...),
    content: str = Form(...),
    rating: int = Form(...),
    avatar: Optional[UploadFile] = File(None),
    current_user: dict = Depends(get_admin_user)
):
    """Update a testimonial with avatar upload (admin only)"""
    update_data = {
        "name": name,
        "role": role,
        "content": content,
        "rating": rating
    }

    if avatar:
        file_location = f"uploads/{uuid.uuid4()}_{avatar.filename}"
        with open(file_location, "wb+") as file_object:
            file_object.write(avatar.file.read())
        update_data["avatar_path"] = file_location

    await db.testimonials.update_one({"_id": ObjectId(testimonial_id)}, {"$set": update_data})
    updated_testimonial = await db.testimonials.find_one({"_id": ObjectId(testimonial_id)})
    if updated_testimonial:
        return serialize_doc(updated_testimonial)
    raise HTTPException(status_code=404, detail="Testimonial not found")

@api_router.delete("/testimonials/{testimonial_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_testimonial(
    testimonial_id: str,
    current_user: dict = Depends(get_admin_user)
):
    """Delete a testimonial (admin only)"""
    delete_result = await db.testimonials.delete_one({"_id": ObjectId(testimonial_id)})
    if delete_result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Testimonial not found")
    return

# SMTP Settings Routes
@api_router.get("/admin/smtp-settings")
async def get_smtp_settings(current_user: dict = Depends(get_admin_user)):
    """Get SMTP settings (admin only)"""
    settings_raw = await db.smtp_settings.find_one({})
    if not settings_raw:
        # Return default settings
        return SMTPSettings().dict()
    
    settings = serialize_doc(settings_raw)
    return settings

@api_router.post("/admin/smtp-settings")
async def update_smtp_settings(
    settings: SMTPSettings,
    current_user: dict = Depends(get_admin_user)
):
    """Update SMTP settings (admin only)"""
    await db.smtp_settings.delete_many({})  # Remove old settings
    await db.smtp_settings.insert_one(settings.dict())
    return {"message": "SMTP settings updated successfully"}

# Initialize default admin user
@api_router.post("/admin/init")
async def initialize_admin():
    """Initialize default admin user (for setup only)"""
    # Check if admin already exists
    admin_exists = await db.users.find_one({"email": "<EMAIL>"})
    if admin_exists:
        return {"message": "Admin user already exists"}
    
    # Create admin user
    admin_data = User(
        name="Admin User",
        email="<EMAIL>",
        password_hash=hash_password("admin123"),
        role="admin"
    )
    
    await db.users.insert_one(admin_data.dict())
    return {"message": "Admin user created successfully"}

# Ad Images Upload & Management Endpoints
@api_router.post("/adimages/upload", response_model=AdImage, status_code=status.HTTP_201_CREATED)
async def upload_ad_image(
    file: UploadFile = File(...),
    current_user: dict = Depends(get_admin_user)
):
    """Upload an ad image (admin only)"""
    # Validate file exists and has content
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")

    # Validate file type
    allowed_types = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"]
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=400,
            detail=f"File type {file.content_type} not allowed. Allowed types: {', '.join(allowed_types)}"
        )

    # Validate file size (max 10MB)
    max_size = 10 * 1024 * 1024  # 10MB in bytes
    file_content = await file.read()
    if len(file_content) == 0:
        raise HTTPException(status_code=400, detail="File is empty")
    if len(file_content) > max_size:
        raise HTTPException(
            status_code=400,
            detail=f"File size {len(file_content)} bytes exceeds maximum allowed size of {max_size} bytes"
        )

    try:
        # Generate unique filename
        file_extension = os.path.splitext(file.filename)[1]
        stored_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(ADIMAGES_DIR, stored_filename)

        # Save file to disk
        with open(file_path, "wb") as f:
            f.write(file_content)

        # Create image metadata
        image_data = AdImage(
            original_filename=file.filename,
            stored_filename=stored_filename,
            file_path=f"uploads/adimages/{stored_filename}",
            file_size=len(file_content),
            mime_type=file.content_type,
            uploaded_by=current_user["id"]
        )

        # Save metadata to database
        await db.adimages.insert_one(image_data.dict())

        return image_data

    except Exception as e:
        # Clean up file if database save fails
        if os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@api_router.get("/adimages", response_model=List[AdImage])
async def get_ad_images(current_user: dict = Depends(get_admin_user)):
    """Get list of all uploaded ad images (admin only)"""
    try:
        images_cursor = db.adimages.find().sort("upload_timestamp", -1)
        images_raw = await images_cursor.to_list(None)
        images = []

        for image in images_raw:
            images.append(serialize_doc(image))

        return images
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve images: {str(e)}")

@api_router.get("/adimages/public", response_model=List[AdImage])
async def get_ad_images_public():
    """Get list of all uploaded ad images (public access for home page)"""
    try:
        images_cursor = db.adimages.find().sort("upload_timestamp", -1)
        images_raw = await images_cursor.to_list(None)
        images = []

        for image in images_raw:
            images.append(serialize_doc(image))

        return images
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to retrieve images: {str(e)}")

@api_router.delete("/adimages/{image_id}")
async def delete_ad_image(
    image_id: str,
    current_user: dict = Depends(get_admin_user)
):
    """Delete an ad image (admin only)"""
    try:
        # Find the image in database
        image_doc = await db.adimages.find_one({"id": image_id})
        if not image_doc:
            raise HTTPException(status_code=404, detail="Image not found")

        # Delete file from disk
        file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), image_doc["file_path"])
        if os.path.exists(file_path):
            os.remove(file_path)

        # Delete from database
        result = await db.adimages.delete_one({"id": image_id})
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Image not found")

        return {"message": "Image deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete image: {str(e)}")

# Health check
@api_router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.utcnow()}
# ML Model Upload & Management Endpoints

# (ML model upload/list/delete endpoints removed as per request)

# Include router
app.include_router(api_router, prefix="/api")

# Serve static files
# Serve static files from the 'uploads' directory
import os
UPLOADS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), "uploads")
ADIMAGES_DIR = os.path.join(UPLOADS_DIR, "adimages")
os.makedirs(UPLOADS_DIR, exist_ok=True)
os.makedirs(ADIMAGES_DIR, exist_ok=True)
app.mount("/uploads", StaticFiles(directory=UPLOADS_DIR), name="uploads")



# Configure logging
logging.basicConfig(level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)