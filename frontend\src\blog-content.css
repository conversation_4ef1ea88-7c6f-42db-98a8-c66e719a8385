/* Blog Content Styling - Ensures proper rendering of TipTap editor content */

/* Base prose styling for blog content */
.blog-content {
  line-height: 1.75;
  color: #374151;
}

.blog-content > * + * {
  margin-top: 1.25em;
}

/* Headings */
.blog-content h1 {
  font-size: 2.25rem;
  font-weight: 800;
  line-height: 1.2;
  color: #111827;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.blog-content h2 {
  font-size: 1.875rem;
  font-weight: 700;
  line-height: 1.3;
  color: #111827;
  margin-top: 1.75rem;
  margin-bottom: 0.875rem;
}

.blog-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  color: #111827;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.blog-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
  color: #111827;
  margin-top: 1.25rem;
  margin-bottom: 0.625rem;
}

.blog-content h5 {
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.4;
  color: #111827;
  margin-top: 1.125rem;
  margin-bottom: 0.5625rem;
}

.blog-content h6 {
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.4;
  color: #111827;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

/* Paragraphs */
.blog-content p {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  line-height: 1.75;
}

/* Links */
.blog-content a {
  color: #7c3aed;
  text-decoration: none;
  font-weight: 500;
}

.blog-content a:hover {
  color: #5b21b6;
  text-decoration: underline;
}

/* Text formatting */
.blog-content strong {
  font-weight: 700;
  color: #111827;
}

.blog-content em {
  font-style: italic;
}

.blog-content u {
  text-decoration: underline;
}

.blog-content s {
  text-decoration: line-through;
}

.blog-content sub {
  vertical-align: sub;
  font-size: 0.75em;
}

.blog-content sup {
  vertical-align: super;
  font-size: 0.75em;
}

/* Lists */
.blog-content ul,
.blog-content ol {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}

.blog-content ul {
  list-style-type: disc;
}

.blog-content ol {
  list-style-type: decimal;
}

.blog-content li {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  line-height: 1.75;
}

.blog-content li p {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

/* Nested lists */
.blog-content ul ul,
.blog-content ol ol,
.blog-content ul ol,
.blog-content ol ul {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.blog-content ul ul {
  list-style-type: circle;
}

.blog-content ul ul ul {
  list-style-type: square;
}

/* Task Lists */
.blog-content ul[data-type="taskList"] {
  list-style: none;
  padding-left: 0;
}

.blog-content ul[data-type="taskList"] li {
  display: flex;
  align-items: flex-start;
  margin: 0.75em 0;
}

.blog-content ul[data-type="taskList"] li > label {
  flex: 0 0 auto;
  margin-right: 0.5rem;
  margin-top: 0.125rem;
  user-select: none;
}

.blog-content ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
}

.blog-content ul[data-type="taskList"] input[type="checkbox"] {
  cursor: pointer;
  margin: 0;
  width: 1rem;
  height: 1rem;
}

.blog-content ul[data-type="taskList"] li[data-checked="true"] > div {
  text-decoration: line-through;
  color: #6b7280;
}

/* Blockquotes */
.blog-content blockquote {
  font-style: italic;
  border-left: 4px solid #7c3aed;
  padding-left: 1.5rem;
  margin: 2rem 0;
  background: #f8fafc;
  padding: 1.5rem;
  border-radius: 0 0.5rem 0.5rem 0;
  color: #4b5563;
}

.blog-content blockquote p {
  margin: 0.5rem 0;
}

/* Code */
.blog-content code {
  background: #f1f5f9;
  color: #dc2626;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 0.875em;
}

.blog-content pre {
  background: #1e293b;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 2rem 0;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  font-size: 0.875em;
  line-height: 1.7;
}

.blog-content pre code {
  background: transparent;
  color: inherit;
  padding: 0;
  border-radius: 0;
  font-size: inherit;
}

/* Tables */
.blog-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  font-size: 0.875em;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.blog-content table th,
.blog-content table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
}

.blog-content table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.blog-content table th:last-child,
.blog-content table td:last-child {
  border-right: none;
}

.blog-content table tr:last-child td {
  border-bottom: none;
}

.blog-content table tr:hover {
  background: #f9fafb;
}

/* Images */
.blog-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 2rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Horizontal Rules */
.blog-content hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 3rem 0;
}

/* Highlights */
.blog-content mark {
  background-color: #fef3c7;
  color: inherit;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .blog-content {
    font-size: 0.95rem;
  }
  
  .blog-content h1 {
    font-size: 1.875rem;
  }
  
  .blog-content h2 {
    font-size: 1.5rem;
  }
  
  .blog-content h3 {
    font-size: 1.25rem;
  }
  
  .blog-content table {
    font-size: 0.8rem;
  }
  
  .blog-content table th,
  .blog-content table td {
    padding: 0.5rem 0.75rem;
  }
  
  .blog-content blockquote {
    padding: 1rem;
    padding-left: 1rem;
    margin: 1.5rem 0;
  }
  
  .blog-content pre {
    padding: 1rem;
    margin: 1.5rem 0;
    font-size: 0.8rem;
  }
}

/* Print styles */
@media print {
  .blog-content {
    color: black;
    font-size: 12pt;
    line-height: 1.4;
  }
  
  .blog-content h1,
  .blog-content h2,
  .blog-content h3,
  .blog-content h4,
  .blog-content h5,
  .blog-content h6 {
    page-break-after: avoid;
    color: black;
  }
  
  .blog-content blockquote,
  .blog-content pre,
  .blog-content table {
    page-break-inside: avoid;
  }
  
  .blog-content a {
    color: black;
    text-decoration: underline;
  }
  
  .blog-content ul[data-type="taskList"] input[type="checkbox"] {
    -webkit-appearance: checkbox;
    appearance: checkbox;
  }
}
