// AdminBlogTab.js
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { 
  FileText, 
  Save, 
  Eye, 
  Upload, 
  Bold, 
  Italic, 
  Underline, 
  Strikethrough,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  List,
  ListOrdered,
  Quote,
  Code,
  Link,
  Image,
  Table,
  Minus,
  Type,
  Palette,
  Highlighter,
  Subscript,
  Superscript,
  Undo,
  Redo,
  Maximize,
  Minimize,
  Search,
  RotateCcw,
  Trash2,
  Edit,
  X
} from 'lucide-react';
import axios from 'axios';
import { toast } from 'react-hot-toast';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import TextStyle from '@tiptap/extension-text-style';
import FontFamily from '@tiptap/extension-font-family';
import Color from '@tiptap/extension-color';
import Highlight from '@tiptap/extension-highlight';
import TextAlign from '@tiptap/extension-text-align';
import Underline from '@tiptap/extension-underline';
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import Strike from '@tiptap/extension-strike';
import Link from '@tiptap/extension-link';
import Image from '@tiptap/extension-image';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import CharacterCount from '@tiptap/extension-character-count';
import Placeholder from '@tiptap/extension-placeholder';
import Focus from '@tiptap/extension-focus';
import Typography from '@tiptap/extension-typography';
import Gapcursor from '@tiptap/extension-gapcursor';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001';
const API = `${BACKEND_URL}/api`;

const AdminBlogTab = () => {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [form, setForm] = useState({
    title: '',
    content: '',
    status: 'draft'
  });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showWordCount, setShowWordCount] = useState(true);
  const [editingBlog, setEditingBlog] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [imageUploading, setImageUploading] = useState(false);
  
  // Auto-save functionality
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const autoSaveInterval = useRef(null);
  const lastSavedContent = useRef('');

  // Font families for the editor
  const fontFamilies = [
    'Arial, sans-serif',
    'Georgia, serif',
    'Times New Roman, serif',
    'Helvetica, sans-serif',
    'Verdana, sans-serif',
    'Courier New, monospace',
    'Trebuchet MS, sans-serif',
    'Palatino, serif',
    'Garamond, serif',
    'Comic Sans MS, cursive'
  ];

  // Font sizes
  const fontSizes = ['8pt', '9pt', '10pt', '11pt', '12pt', '14pt', '16pt', '18pt', '20pt', '24pt', '28pt', '32pt', '36pt', '48pt', '72pt'];

  // TipTap editor configuration
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        history: {
          depth: 100,
        },
      }),
      TextStyle,
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Color.configure({
        types: ['textStyle'],
      }),
      Highlight.configure({
        multicolor: true,
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Underline,
      Subscript,
      Superscript,
      Strike,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline hover:text-blue-800',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg',
        },
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      HorizontalRule,
      CharacterCount,
      Placeholder.configure({
        placeholder: 'Start writing your blog post content here...',
      }),
      Focus.configure({
        className: 'has-focus',
        mode: 'all',
      }),
      Typography,
      Gapcursor,
    ],
    content: form.content,
    onUpdate: ({ editor }) => {
      const newContent = editor.getHTML();
      setForm(prev => ({ ...prev, content: newContent }));
      
      // Auto-save functionality
      if (autoSaveEnabled && newContent !== lastSavedContent.current) {
        clearTimeout(autoSaveInterval.current);
        autoSaveInterval.current = setTimeout(() => {
          handleAutoSave(newContent);
        }, 2000); // Auto-save after 2 seconds of inactivity
      }
    },
    editorProps: {
      attributes: {
        class: 'min-h-[500px] p-4 focus:outline-none prose prose-lg max-w-none',
      },
    },
  });

  useEffect(() => {
    fetchBlogs();
    return () => {
      if (autoSaveInterval.current) {
        clearTimeout(autoSaveInterval.current);
      }
    };
  }, []);

  // Update editor content when form content changes
  useEffect(() => {
    if (editor && form.content !== editor.getHTML()) {
      editor.commands.setContent(form.content);
    }
  }, [form.content, editor]);

  const fetchBlogs = async () => {
    setLoading(true);
    try {
      const res = await axios.get(`${API}/blogs`);
      setBlogs(res.data);
    } catch (error) {
      toast.error('Failed to fetch blog posts');
      console.error('Error fetching blogs:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAutoSave = async (content) => {
    if (!editingBlog || !form.title.trim()) return;
    
    try {
      await axios.put(`${API}/blogs/${editingBlog.id}`, {
        ...form,
        content,
        status: 'draft'
      });
      lastSavedContent.current = content;
      toast.success('Auto-saved', { duration: 1000 });
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
  };

  const handleSave = async (status = 'draft') => {
    if (!form.title.trim()) {
      toast.error('Please enter a blog title');
      return;
    }
    
    if (!form.content.trim()) {
      toast.error('Please enter blog content');
      return;
    }

    setSaving(true);
    try {
      const blogData = { ...form, status };
      
      if (editingBlog) {
        await axios.put(`${API}/blogs/${editingBlog.id}`, blogData);
        toast.success(`Blog ${status === 'published' ? 'published' : 'saved'} successfully`);
      } else {
        await axios.post(`${API}/blogs`, blogData);
        toast.success(`Blog ${status === 'published' ? 'published' : 'created'} successfully`);
      }
      
      lastSavedContent.current = form.content;
      fetchBlogs();
      
      if (status === 'published') {
        resetForm();
      }
    } catch (error) {
      const errorMessage = error.response?.data?.detail || `Failed to ${status === 'published' ? 'publish' : 'save'} blog`;
      toast.error(errorMessage);
      console.error('Error saving blog:', error);
    } finally {
      setSaving(false);
    }
  };

  const resetForm = () => {
    setForm({ title: '', content: '', status: 'draft' });
    setEditingBlog(null);
    if (editor) {
      editor.commands.setContent('');
    }
    lastSavedContent.current = '';
  };

  const handleEdit = (blog) => {
    setEditingBlog(blog);
    setForm({
      title: blog.title,
      content: blog.content,
      status: blog.status
    });
    if (editor) {
      editor.commands.setContent(blog.content);
    }
    lastSavedContent.current = blog.content;
  };

  const handleDelete = async (id) => {
    if (!window.confirm('Are you sure you want to delete this blog post?')) return;
    
    try {
      await axios.delete(`${API}/blogs/${id}`);
      toast.success('Blog post deleted successfully');
      fetchBlogs();
      
      if (editingBlog && editingBlog.id === id) {
        resetForm();
      }
    } catch (error) {
      const errorMessage = error.response?.data?.detail || 'Failed to delete blog post';
      toast.error(errorMessage);
      console.error('Error deleting blog:', error);
    }
  };

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}>
      <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
        <FileText className="w-5 h-5" /> Blog Management
      </h2>
      
      {/* Blog Editor Form */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">
            {editingBlog ? 'Edit Blog Post' : 'Create New Blog Post'}
          </h3>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setAutoSaveEnabled(!autoSaveEnabled)}
              className={`px-3 py-1 text-sm rounded ${autoSaveEnabled ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'}`}
            >
              Auto-save: {autoSaveEnabled ? 'ON' : 'OFF'}
            </button>
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 text-gray-600 hover:text-gray-800 rounded"
              title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
            >
              {isFullscreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
            </button>
          </div>
        </div>

        {/* Title Input */}
        <div className="mb-4">
          <label className="block mb-2 font-medium">Blog Title *</label>
          <input
            type="text"
            name="title"
            value={form.title}
            onChange={handleInputChange}
            placeholder="Enter your blog post title..."
            className="w-full border border-gray-300 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            required
          />
        </div>

        {/* Rich Text Editor will be added in the next chunk */}
      </div>
    </div>
  );
};

export default AdminBlogTab;
