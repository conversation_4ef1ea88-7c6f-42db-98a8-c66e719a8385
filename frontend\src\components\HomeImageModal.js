// HomeImageModal.js - Image modal for home page that fetches from API
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { X } from 'lucide-react';
import axios from 'axios';
import './ImageModal.css';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001';
const API = `${BACKEND_URL}/api`;

const HomeImageModal = ({ show, onClose }) => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Refs for cleanup
  const autoSlideInterval = useRef(null);

  // Fetch images from API (public endpoint - no auth required)
  const fetchImages = async () => {
    setLoading(true);
    try {
      // Try to fetch images without authentication first (for public access)
      const response = await axios.get(`${API}/adimages/public`);
      setImages(response.data);
    } catch (error) {
      // If public endpoint doesn't exist, try with potential auth
      try {
        const response = await axios.get(`${API}/adimages`);
        setImages(response.data);
      } catch (authError) {
        console.log('No images available or authentication required');
        setImages([]);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (show) {
      fetchImages();
    }
  }, [show]);

  // Modal helper functions
  const closeModal = () => {
    clearInterval(autoSlideInterval.current);
    onClose();
  };

  const nextImage = useCallback(() => {
    if (images.length > 0) {
      setCurrentImageIndex((prev) => (prev + 1) % images.length);
    }
  }, [images.length]);

  // Auto-slide effect - simple auto change every 4 seconds
  useEffect(() => {
    if (show && images.length > 1) {
      autoSlideInterval.current = setInterval(() => {
        nextImage();
      }, 4000); // 4 seconds interval
    } else {
      clearInterval(autoSlideInterval.current);
    }

    return () => clearInterval(autoSlideInterval.current);
  }, [show, images.length, nextImage]);

  // Simple keyboard navigation - only Escape to close
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (!show) return;

      if (e.key === 'Escape') {
        closeModal();
      }
    };

    if (show) {
      document.addEventListener('keydown', handleKeyPress);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    return () => {
      document.removeEventListener('keydown', handleKeyPress);
      document.body.style.overflow = 'unset';
    };
  }, [show]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearInterval(autoSlideInterval.current);
      document.body.style.overflow = 'unset';
    };
  }, []);

  // Don't render if not showing or no images
  if (!show) return null;

  // Loading state
  if (loading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center animate-fadeIn">
        <div 
          className="absolute inset-0 bg-black bg-opacity-90 transition-opacity duration-300 backdrop-blur-sm"
          onClick={closeModal}
        />
        <div className="relative text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p>Loading images...</p>
        </div>
      </div>
    );
  }

  // No images state
  if (images.length === 0) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center animate-fadeIn">
        <div 
          className="absolute inset-0 bg-black bg-opacity-90 transition-opacity duration-300 backdrop-blur-sm"
          onClick={closeModal}
        />
        <div className="relative text-white text-center p-8">
          <h3 className="text-xl font-semibold mb-4">No Images Available</h3>
          <p className="text-gray-300 mb-6">There are currently no advertisement images to display.</p>
          <button
            onClick={closeModal}
            className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center animate-fadeIn">
      {/* Overlay */}
      <div
        className="absolute inset-0 bg-black bg-opacity-80 transition-opacity duration-300"
        onClick={closeModal}
      />

      {/* Image Display - 70% of screen */}
      <div className="relative flex items-center justify-center" style={{ width: '70vw', height: '70vh' }}>
        <img
          src={`${BACKEND_URL}/${images[currentImageIndex].file_path}`}
          alt={images[currentImageIndex].original_filename}
          className="w-full h-full object-cover rounded-lg shadow-2xl transition-opacity duration-500"
          style={{ width: '100%', height: '100%' }}
        />

        {/* Close button - always visible in top-right */}
        <button
          onClick={closeModal}
          className="absolute top-4 right-4 text-white hover:text-gray-300 transition-all duration-200 p-3 rounded-full bg-black bg-opacity-60 hover:bg-opacity-80 hover:scale-110 shadow-lg z-10"
          title="Close (Esc)"
        >
          <X className="w-6 h-6" />
        </button>
      </div>
    </div>
  );
};

export default HomeImageModal;
